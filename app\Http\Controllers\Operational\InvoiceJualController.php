<?php

namespace App\Http\Controllers\Operational;

use App\Abstracts\Finance\Account as COA;
use App\Abstracts\Inventory\Item;
use App\Abstracts\JobOrderDetail as JOD;
use App\Abstracts\Operational\Invoice as AppInvoice;
use App\Abstracts\Operational\InvoiceDetail as InvDetail;
use App\Abstracts\Sales\SalesOrder;
use App\Exports\Operational\Invoice1Export;
use App\Exports\Operational\Invoice2Export;
use App\Exports\Operational\InvoiceExport;
use App\Exports\Operational\InvoiceFastanaExport;
use App\Http\Controllers\Controller;
use App\Model\Account;
use App\Model\AccountDefault;
use App\Model\CashTransaction;
use App\Model\CashTransactionDetail;
use App\Model\City;
use App\Model\Closing;
use App\Model\Commodity;
use App\Model\Company;
use App\Model\Contact;
use App\Model\CostType;
use App\Model\Invoice;
use App\Model\InvoiceDetail;
use App\Model\InvoiceJoin;
use App\Model\InvoiceTax;
use App\Model\JobOrder;
use App\Model\JobOrderCost;
use App\Model\JobOrderDetail;
use App\Model\Journal;
use App\Model\JournalDetail;
use App\Model\Manifest;
use App\Model\Notification;
use App\Model\NotificationType;
use App\Model\NotificationTypeUser;
use App\Model\NotificationUser;
use App\Model\PriceList;
use App\Model\QuotationDetail;
use App\Model\Receivable;
use App\Model\ReceivableDetail;
use App\Model\SalesOrder as ModelSalesOrder;
use App\Model\Tax;
use App\Model\TypeTransaction;
use App\Model\VehicleType;
use App\Model\WorkOrder;
use App\Utils\TransactionCode;
use Barryvdh\DomPDF\Facade as PDF;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Response;

class InvoiceJualController extends Controller
{
  /*
      Date : 24-03-2020
      Description : Mengambil remark
      Developer : Didin
      Status : Create
    */
  protected $remark;
  public function __construct()
  {
    $this->remark = DB::table('print_remarks')->first();
  }
  public function index()
  {
    $data['customer_list'] = DB::table('invoices')->leftJoin('contacts', 'contacts.id', 'invoices.customer_id')->groupBy('invoices.customer_id')->selectRaw('contacts.id,contacts.name,group_concat(invoices.id) as invoice_list')->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  /**
   * Show the form for creating a new resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function create(Request $request)
  {
    $code = new TransactionCode(1, 'Handling');
    $code->setCode();
    $trx_code = $code->getCode();
    $data['account'] = Account::with('parent')->whereRaw("is_base = 0")->select('id', 'code', 'name')->get();
    // $data['cost_type']=CostType::with('parent')->where('parent_id','!=',null)->where('is_invoice', 1)->get();
    $data['cost_type'] = DB::table('cost_types')
      ->leftJoin('cost_types as parent', 'parent.id', '=', 'cost_types.parent_id')
      ->leftJoin('companies', 'companies.id', '=', 'cost_types.company_id')
      ->where('cost_types.parent_id', '!=', null)
      ->where('cost_types.is_invoice', 1)
      ->select([
        'cost_types.id',
        DB::raw("CONCAT(cost_types.name,' - ',companies.name) as name"),
        'cost_types.qty',
        'cost_types.vendor_id',
        'cost_types.cost as price',
        'cost_types.initial_cost as total_price',
        DB::raw("concat(null) as job_order_id"),
        'parent.name as parent'
      ])->get();
    $data['tax'] = Tax::all();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function get_job_order_costs(Request $request)
  {
    $data['cost_type'] = DB::table('cost_types')
      ->leftJoin('cost_types as parent', 'parent.id', '=', 'cost_types.parent_id')
      ->join('job_order_costs', 'cost_types.id', '=', 'cost_type_id')
      ->where('job_order_costs.type', 2)
      ->where('cost_types.parent_id', '!=', null)
      ->where('header_id', $request->job_order_id)
      ->select([
        'cost_types.id',
        'cost_types.name',
        'job_order_costs.qty',
        'cost_types.vendor_id',
        'job_order_costs.price as price',
        'job_order_costs.total_price as total_price',
        DB::raw("concat(null) as job_order_id"),
        'parent.name as parent'
      ])->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  /*
      Date : 18-03-2020
      Description : Menambah invoice
      Developer : Didin
      Status : Edit
    */
  /*
      Date : 23-03-2022
      Description : Menyesuaikan penambahan invoice untuk Sales Order (28-07-2021, Hendra)
      Description : Menambahkan kolom no po customer
      Developer : Dimas Ihsan
      Status : Edit
    */
  //start refactore store invoice
  public function store(Request $request)
  {
    // Validate that all details have the same sales order status
    $statusSO = collect($request->detail)->pluck('sales_order_status_id')->unique()->filter()->count();
    if ($statusSO > 1) {
      return response()->json(['message' => 'Detail Invoice harus memiliki status Sales Order yang sama'], 421);
    }
    // Merge payment type into the request if applicable
    $typeBayarSo = collect($request->detail)->first();
    if (isset($typeBayarSo['payment_type']) && $typeBayarSo['payment_type'] == 1) {
      $request->merge(['type_bayar' => 1]);
    }
    // Set the default cash account if payment type is 1
    $defaultAccountCash = ($request->type_bayar == 1) ? AccountDefault::first() : null;
    // Start transaction
    DB::beginTransaction();
    try {
      // Generate transaction code
      $code = new TransactionCode($request->company_id, 'invoice');
      $code->setCode();
      $trx_code = $code->getCode();
      // Create the Invoice
      $invoice = Invoice::create([
        'company_id' => $request->company_id,
        'customer_id' => $request->customer_id,
        'account_cash_id' => $request->type_bayar == 1 ? ($request->cash_account_id ?? $defaultAccountCash->account_cash) : null,
        'type_transaction_id' => 26,
        'description' => $request->description,
        'catatan' => $request->catatan,
        'account_receivable_id' => COA::getReceivable(),
        'termin' => $request->termin,
        'due_date' => ($request->type_bayar == 2 ? Carbon::parse($request->date_invoice)->addDays($request->termin) : null),
        'type_bayar' => $request->type_bayar,
        'create_by' => auth()->id(),
        'code' => $trx_code,
        'date_invoice' => dateDB($request->date_invoice),
        'journal_date' => dateDB($request->journal_date),
        'status' => 2,
        'sub_total' => round($request->sub_total),
        'discount_total' => round($request->discount_total),
        'is_ppn' => $request->is_ppn,
        'total_another_ppn' => round($request->total_another_ppn),
        'grand_total' => round($request->grand_total),
        'is_npwp' => $request->is_npwp,
        'ppn' => round($request->ppn_total),
        'no_po_customer' => $request->no_po_customer,
        'city_from' => $request->city_from ?? null,
        'city_to' => $request->city_to ?? null,
        'start_date' => $request->start_date ?? null,
        'end_date' => $request->end_date ?? null,
      ]);
    
      $jo_ids = [];
      $invoiceDetails = [];
      $invoiceTaxes = [];
      
      foreach ($request->detail as $key => $detail) {
        if (empty($detail)) {
          continue;
        }
        // Collect Job Order IDs
        $jo_ids[] = $detail['job_order_id'] ?? null;
        
        $manifest = DB::table('manifests')->select('manifests.*')
          ->join('manifest_details', 'manifest_details.header_id', 'manifests.id')
          ->join('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
          ->where('job_order_details.header_id', $detail['job_order_id'])->first();
        
        $invoiceDetails[] = [
          'header_id' => $invoice->id,
          'job_order_id' => $detail['job_order_id'] ?? null,
          'job_order_detail_id' => $detail['job_order_detail_id'] ?? null,
          'work_order_id' => $detail['work_order_id'] ?? null,
          'cost_type_id' => $detail['cost_type_id'] ?? null,
          'price' => $detail['price'],
          'total_price' => $detail['total_price'],
          'imposition' => $detail['imposition'] ?? 1,
          'qty' => $detail['qty'],
          'description' => $detail['description'] ?? '-',
          'is_other_cost' => $detail['is_other_cost'] ?? 0,
          'type_other_cost' => $detail['type_other_cost'] ?? 0,
          'manifest_id' => $manifest->id ?? null,
          'create_by' => auth()->id(),
          'imposition_name' => $detail['imposition_name'] ?? '-',
          'commodity_name' => $detail['commodity_name'] ?? '-',
          'discount' => round($detail['discount']),
          'is_ppn' => $detail['is_ppn'] ?? 0,
          'ppn' => round($detail['ppn']),
          'total_tax' => $detail['total_tax'] ?? 0,
          'created_at' => now(),
          'updated_at' => now(),
        ];
      }
      
      // Batch insert Invoice Details
      if (!empty($invoiceDetails)) {
        InvoiceDetail::insert($invoiceDetails);
        
        // Get the created invoice details with their actual IDs
        $createdInvoiceDetails = InvoiceDetail::where('header_id', $invoice->id)
          ->orderBy('id')
          ->get();
        
        foreach ($request->detail as $key => $detail) {
          if (empty($detail)) {
            continue;
          }
          
          $invoiceDetailId = $createdInvoiceDetails[$key]->id ?? null;
          if (!$invoiceDetailId) {
            continue;
          }
          
          // Prepare Invoice Tax data for batch insert
          foreach ($detail['detail_tax'] as $taxDetail) {
            if (empty($taxDetail['tax_id']) || $taxDetail['tax_id'] === null || $taxDetail['tax_id'] === 0) {
              continue;
            }
            
            // Skip if value/amount is 0 or empty (check both 'value' and calculated amount)
            $tax = DB::table('taxes')->where('id', $taxDetail['tax_id'])->first();
            $valuePajak = $tax ? ($request->is_npwp == 1 ? $tax->npwp : $tax->non_npwp) : 0;
            $amountValue = isset($detail['total_with_discount']) ? ($valuePajak / 100) * $detail['total_with_discount'] : $taxDetail['value'];
            
            if (empty($amountValue) || $amountValue == 0) {
              continue;
            }
            
            $invoiceTaxes[] = [
              'header_id' => $invoice->id,
              'invoice_detail_id' => $invoiceDetailId, // Use actual invoice detail ID
              'tax_id' => $taxDetail['tax_id'],
              'amount' => $amountValue,
              'value_pajak' => $valuePajak,
              'created_at' => now(),
              'updated_at' => now(),
            ];
          }
        }
        
        // Batch insert Invoice Taxes
        if (!empty($invoiceTaxes)) {
          InvoiceTax::insert($invoiceTaxes);
        }
      }
      // Update Job Orders, Work Orders, and Manifests
      $this->updateRelatedEntities($jo_ids, $invoice, $trx_code, $request->date_invoice);
      // Create notification
      $this->createNotification($request, $trx_code, $invoice->id);
      // Commit transaction
      DB::commit();
      return response()->json(null);
    } catch (\Exception $e) {
      DB::rollBack();
      // Log the error message for debugging
      Log::error('Invoice Processing Error: ' . $e->getMessage());
      return response()->json(['message' => 'An error occurred while processing the invoice'], 500);
    }
  }
  /**
   * Update related Job Orders, Work Orders, and Manifests.
   */
  private function updateRelatedEntities(array $jo_ids, $invoice, string $trx_code, string $date_invoice)
  {
    $jobOrders = JobOrder::whereIn('id', $jo_ids)->get();
    foreach ($jobOrders as $jobOrder) {
      DB::table('invoice_job_order')->insert([
        'invoice_id' => $invoice->id,
        'job_order_id' => $jobOrder->id,
        'created_at' => now(),
        'updated_at' => now()
      ]);
      $jobOrder->update([
        'code_invoice' => $trx_code,
        'date_invoice' => dateDB($date_invoice),
        'status' => 3,
        'is_invoiced' => 1,
      ]);
      if ($jobOrder->work_order_id) {
        $unInvoicedJobs = JobOrder::where('work_order_id', $jobOrder->work_order_id)->whereNull('is_invoiced')->count();
        if ($unInvoicedJobs == 0) {
          WorkOrder::find($jobOrder->work_order_id)->update(['is_invoice' => 1]);
        }
      }
      if ($jobOrder->manifest_id) {
        Manifest::find($jobOrder->manifest_id)->update(['is_invoice' => 1]);
      }
    }
  }
  /**
   * Create Notification for the invoice creation.
   */
  private function createNotification($request, $trx_code, $invoice_id)
  {
    $slug = str_random(6);
    $customer = Contact::find($request->customer_id);
    $userList = DB::table('notification_type_users')
      ->leftJoin('users', 'users.id', '=', 'notification_type_users.user_id')
      ->where('notification_type_users.notification_type_id', 14)
      ->select('users.id', 'users.is_admin', 'users.company_id')
      ->get();
    $notification = Notification::create([
      'notification_type_id' => 14,
      'name' => 'Invoice Jual Baru telah Dibuat!',
      'description' => 'No. Invoice Jual ' . $trx_code . ' nama customer ' . $customer->name,
      'slug' => $slug,
      'route' => 'operational.invoice_jual.show',
      'parameter' => json_encode(['id' => $invoice_id]),
    ]);
    foreach ($userList as $user) {
      if ($user->company_id == $request->company_id) {
        NotificationUser::create([
          'notification_id' => $notification->id,
          'user_id' => $user->id,
        ]);
      }
    }
  }
  // end of refactore store invoice //
  /*
      Date : 25-03-2022
      Description : Menampilkan detail invoice jual (09-03-2020, Didin)
      Description : Menambahkan untuk ambil data komoditas, kota, tipe kendaraan
      Developer : Dimas Ihsan
      Status : Edit
    */
  public function show($id)
  {
    $data['item'] = Invoice::with('company', 'customer', 'journal', 'jobOrders')
      ->leftJoin('tax_invoices', 'tax_invoices.invoice_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('invoices.*, tax_invoices.code AS tax_invoice_code')
      ->first();
    $data['commodities'] = Commodity::select('id', 'name')->get();
    $data['cities'] = City::select('id', 'name')->get();
    $data['vehicle_types'] = VehicleType::select('id', 'name')->get();
    $data['taxes'] = InvoiceTax::where('header_id', $id)->sum('amount');
    $data['addon'] = DB::select("select group_concat(distinct job_orders.aju_number SEPARATOR ', ') as aju,group_concat(distinct job_orders.no_bl SEPARATOR ', ') as bl, GROUP_CONCAT(distinct work_orders.code SEPARATOR ', ') as code_wo from invoice_details left join job_orders on job_orders.id = invoice_details.job_order_id
        left join work_orders on work_orders.id = job_orders.work_order_id where invoice_details.header_id = $id")[0];
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function showDetail($id)
  {
    $taxcount = DB::table('taxes')
      ->count('id');
    $detail1 = InvoiceDetail::with(
      'job_order',
      'job_order.detail.item',
      'job_order.detail.manifest_detail',
      'job_order.detail.piece',
      'manifest.driver',
      'manifest.container',
      'manifest.vehicle',
      'manifest.details',
      'job_order.commodity:id,name',
      'job_order.service:id,name,service_type_id',
      'job_order.trayek',
      'job_order.trayek.from:id,name',
      'job_order.trayek.to:id,name',
      'cost_type:id,name,description',
      'job_order.sales_order.customer_order:id,payment_type'
    )
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_orders.id', 'job_order_details.header_id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->select(
        'invoice_details.*',
        'manifest_details.customer_sj as dod_code',
        DB::raw("(SELECT SUM(transported) FROM manifest_details JOIN manifests ON manifests.id = manifest_details.header_id WHERE job_order_detail_id = job_order_details.id) AS transported_item"),
      )
      ->where('invoice_details.header_id', $id)
      ->get();
    // dd($detail1[0]);
    foreach ($detail1 as $detail) {
      $detail['total_price_tabel'] = $detail->price * $detail->qty;
      $taxes = [];
      $invoice_taxes = DB::table('invoice_taxes')
        ->whereNotNull('tax_id')
        ->whereInvoiceDetailId($detail->id)
        ->get();
      if ($detail->job_order_id) {
        if ($detail->job_order->quotation_id != null) {
          $quotation = DB::table('quotations')
            ->whereId($detail->job_order->quotation_id)
            ->select('bill_type')
            ->first();
          if (($quotation->bill_type ?? null) == 2) {
            $work_order = DB::table('work_orders')
              ->whereId($detail->job_order->work_order_id)
              ->select('name')
              ->first();
            $detail->job_order->service->name = $work_order->name;
          }
        }
      }
      foreach ($invoice_taxes as $tax) {
        $pajak = DB::table('taxes')->select('is_ppn')->where('id', $tax->tax_id)->first();
        array_push($taxes, [
          'id' => $tax->id,
          'tax_id' => $tax->tax_id,
          'is_ppn' => $pajak->is_ppn,
          'value' => $tax->amount
        ]);
      }
      for ($i = count($taxes) - 1; $i < $taxcount; $i++) {
        array_push($taxes, [
          'tax_id' => null,
          'value' => 0
        ]);
      }
      $detail->detail_tax = $taxes;
    }
    $grand_total_pajak_ppn = 0;
    $grand_total_pajak_non_ppn = 0;
    foreach ($detail1 as $detail) {
      foreach ($detail->detail_tax as $tax) {
        if ($tax['tax_id']) {
          if ($tax['is_ppn']) {
            $grand_total_pajak_ppn += $tax['value'];
          } else {
            $grand_total_pajak_non_ppn += $tax['value'];
          }
          $detail->total_pajak = $grand_total_pajak_ppn + $grand_total_pajak_non_ppn;
        }
      }
    }
    $detail1[0]['grand_total_pajak_ppn'] = $grand_total_pajak_ppn;
    $detail1[0]['grand_total_pajak_non_ppn'] = $grand_total_pajak_non_ppn;
    return response()->json($detail1, 200, [], JSON_NUMERIC_CHECK);
  }
  public function edit($id)
  {
    //
  }
  /**
   * Update the specified resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function update(Request $request, $id)
  {
    ini_set('max_execution_time', 300);
    $request->validate([
      'company_id' => 'required',
      'journal_date' => 'required',
      'customer_id' => 'required',
      'type_bayar' => 'required',
      // 'cash_account_id' => 'required_if:type_bayar,1',
      'termin' => 'required_if:type_bayar,2',
      'grand_total' => 'required|integer|min:1',
    ], [
      'company_id.required' => 'Company tidak boleh kosong',
      'journal_date.required' => 'Tanggal tidak boleh kosong',
      'customer_id.required' => 'Customer tidak boleh kosong',
      'type_bayar.required' => 'Tipe bayar tidak boleh kosong',
      // 'cash_account_id.required_if' => 'Cash Account tidak boleh kosong',
      'termin.required_if' => 'Termin tidak boleh kosong',
      'grand_total.required' => 'Grand Total tidak boleh kosong',
      'grand_total.integer' => 'Grand Total harus berupa angka',
      'grand_total.min:1' => 'Grand Total harus ebih dari 0',
    ]);
    $statusSO = collect($request->detail)->pluck('sales_order_status_id')->unique()
      ->filter(function ($x) {
        if ($x != null) {
          return $x;
        }
      })->count();
    if ($statusSO > 1) {
      return response()->json(['message' => 'Detail Invoice harus memiliki status Sales Order yang sama'], 422);
    }
    $typeBayarSo = collect($request->detail)->first();
    if (isset($typeBayarSo['payment_type']) && $typeBayarSo['payment_type'] == 1) {
      $request->merge(['type_bayar' => 1]);
    }
    if ($request->type_bayar == 1) {
      $defaultAccountCash = AccountDefault::first();
      if (!$defaultAccountCash->account_cash) {
        return response()->json(['message' => 'Default Account Cash belum diatur. Silahkan lakukan atur pada Setting / Keuangan / Setting Akun'], 422);
      }
    }
    DB::beginTransaction();
    $i = Invoice::find($id);
    $invoiceCode = $i->code;
    $i->update([
      'code' => $request->code,
      'company_id' => $request->company_id,
      'customer_id' => $request->customer_id,
      'account_cash_id' => $request->type_bayar == 1 ? ($request->cash_account_id ?? $defaultAccountCash->account_cash) : null,
      'description' => $request->description,
      'catatan' => $request->catatan,
      'account_receivable_id' => $request->account_receivable_id,
      'termin' => $request->termin,
      'due_date' => ($request->type_bayar == 2 ? (Carbon::parse($request->date_invoice)->addDays($request->termin)) : null),
      'type_bayar' => $request->type_bayar,
      'create_by' => auth()->id(),
      'date_invoice' => dateDB($request->date_invoice),
      'journal_date' => dateDB($request->journal_date),
      'sub_total' => round($request->sub_total),
      // 'discount_percent' => $request->discount_percent,
      'discount_total' => round($request->discount_total),
      'is_ppn' => $request->is_ppn,
      'total_another_ppn' => round($request->total_another_ppn),
      'grand_total' => round($request->grand_total),
      'is_npwp' => $request->is_npwp,
      'ppn' => $request->ppn_total,
    ]);
    $i->increment('qty_edit', 1);
    $listJo = collect($request->detail)->where('cost_type_id', null)->pluck('job_order_id')->toArray();
    $ivd = DB::table('invoice_details')->where('header_id', $id)->where('cost_type_id', null)->select('job_order_id')->get();
    // return response()->json($ivd,500);
    InvoiceTax::whereHeaderId($id)->delete();
    DB::table('invoice_details')->whereHeaderId($id)->delete();
    
    $invoiceDetails = [];
    $invoiceTaxes = [];
    
    foreach ($request->detail as $key => $value) {
      if (empty($value)) {
        continue;
      }
      
      $invoiceDetails[] = [
        'header_id' => $id,
        'job_order_id' => $value['job_order_id'] ?? null,
        'job_order_detail_id' => $value['job_order_detail_id'],
        'work_order_id' => $value['work_order_id'],
        'cost_type_id' => $value['cost_type_id'],
        'price' => $value['price'],
        'total_price' => $value['total_price'],
        'imposition' => $value['imposition'] ?? 1,
        'qty' => $value['qty'],
        'description' => $value['description'] ?? '-',
        'is_other_cost' => $value['is_other_cost'],
        'is_other_cost' => $value['is_other_cost'],
        'type_other_cost' => $value['type_other_cost'],
        'manifest_id' => $value['manifest_id'] ?? null,
        'create_by' => auth()->id(),
        'imposition_name' => $value['imposition_name'] ?? '-',
        'commodity_name' => $value['commodity_name'] ?? '-',
        'discount' => round($value['discount']),
        'is_ppn' => $value['is_ppn'] ?? 0,
        'ppn' => round($value['ppn']),
        'total_tax' => $value['total_tax'] ?? 0,
        'created_at' => now(),
        'updated_at' => now(),
      ];
    }
    
    // Batch insert Invoice Details
    if (!empty($invoiceDetails)) {
      InvoiceDetail::insert($invoiceDetails);
      
      // Get the created invoice details with their actual IDs
      $createdInvoiceDetails = InvoiceDetail::where('header_id', $id)
        ->orderBy('id')
        ->get();
      
      foreach ($request->detail as $key => $value) {
        if (empty($value)) {
          continue;
        }
        
        $invoiceDetailId = $createdInvoiceDetails[$key]->id ?? null;
        if (!$invoiceDetailId) {
          continue;
        }
        
        // Prepare Invoice Tax data for batch insert
        foreach ($value['detail_tax'] as $vws) {
          // Skip if tax_id is null, empty, or 0
          if (empty($vws['tax_id']) || $vws['tax_id'] === null || $vws['tax_id'] === 0) {
            continue;
          }
          
          // Skip if value/amount is 0 or empty
          if (empty($vws['value']) || $vws['value'] == 0) {
            continue;
          }
          
          // Get tax information for value_pajak calculation
          $tax = DB::table('taxes')->where('id', $vws['tax_id'])->first();
          $valuePajak = $tax ? ($request->is_npwp == 1 ? $tax->npwp : $tax->non_npwp) : 0;
          
          $invoiceTaxes[] = [
            'header_id' => $id,
            'invoice_detail_id' => $invoiceDetailId, // Use actual invoice detail ID
            'tax_id' => $vws['tax_id'],
            'amount' => $vws['value'],
            'value_pajak' => $valuePajak,
            'created_at' => now(),
            'updated_at' => now(),
          ];
        }
      }
      
      // Batch insert Invoice Taxes
      if (!empty($invoiceTaxes)) {
        InvoiceTax::insert($invoiceTaxes);
      }
    }
    
    foreach ($request->detail as $key => $value) {
      if (empty($value)) {
        continue;
      }
      if (isset($value['job_order_id'])) {
        JobOrder::find($value['job_order_id'])->update([
          // 'invoice_id' => $id,
          'code_invoice' => $i->first()->code,
          'date_invoice' => dateDB($request->date_invoice),
          'status' => 3,
          'is_invoiced' => 1
        ]);
        $jo = JobOrder::find($value['job_order_id']);
        // $cekwo=DB::table('job_orders')->whereRaw("work_order_id = $jo->work_order_id and invoice_id is null")->count();
        $cekwo = DB::table('job_orders')->whereRaw("work_order_id = $jo->work_order_id")->count();
        if ($cekwo < 1) {
          WorkOrder::find($jo->work_order_id)->update([
            'is_invoice' => 1
          ]);
        }
        if ($jo != null) {
          DB::table('invoice_job_order')->updateOrInsert([
            'invoice_id' => $i->id,
            'job_order_id' => $jo->id
          ], [
            'invoice_id' => $i->id,
            'job_order_id' => $jo->id,
            'created_at' => now(),
            'updated_at' => now()
          ]);
        }
      }
      if (isset($value['manifest_id'])) {
        Manifest::find($value['manifest_id'])->update([
          'is_invoice' => 1
        ]);
      }
      if (isset($value['work_order_id'])) {
        // JobOrder::where('work_order_id', $value['work_order_id'])->where('invoice_id',null)->update([
        JobOrder::where('work_order_id', $value['work_order_id'])->update([
          // 'invoice_id' => $id,
          'code_invoice' => $invoiceCode,
          'date_invoice' => dateDB($request->date_invoice),
          'status' => 3
        ]);
        WorkOrder::find($value['work_order_id'])->update([
          'is_invoice' => 1
        ]);
      }
    }
    $this->storeInvoiceInWorkOrder();
    InvDetail::setServiceId($id);
    foreach ($ivd as $v) {
      if (!in_array($v->job_order_id, $listJo)) {
        DB::table('job_orders')->where('id', $v->job_order_id)->update([
          // 'invoice_id' => null,
          'status' => 2,
          'code_invoice' => null,
          'date_invoice' => null,
          'is_invoiced' => 0
        ]);
        DB::table('invoice_job_order')->where('job_order_id', $v->job_order_id)->delete();
        $q = "select m.id from manifests m
            join manifest_details md on md.header_id=m.id
            join job_order_details jod on jod.id=md.job_order_detail_id
            join job_orders jo on jo.id=jod.header_id
            where jo.id=$v->job_order_id";
        $mans = DB::select($q);
        if (!empty($mans)) {
          foreach ($mans as $m) {
            Manifest::find($m->id)->update([
              'is_invoice' => 0
            ]);
          }
        }
      }
    }
    DB::commit();
    return Response::json(['message' => 'Data berhasil diupdate']);
  }
  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroy($id)
  {
    DB::beginTransaction();
    $dataManifest = InvoiceDetail::where('header_id', $id)->get();
    foreach ($dataManifest as $key => $value) {
      $work_order_id = $value->work_order_id;
      if ($work_order_id != null) {
        WorkOrder::find($work_order_id)->update([
          'is_invoice' => 0
        ]);
      }
      Manifest::where('id', '=', $value->manifest_id)->update([
        'is_invoice' => 0
      ]);
      if (!empty($value->job_order_id)) {
        JobOrder::where('id', '=', $value->job_order_id)->update([
          // 'invoice_id' => null,
          'code_invoice' => null,
          'date_invoice' => null,
          'is_invoiced' => 0
        ]);
        DB::table('invoice_job_order')->where('job_order_id', $value->job_order_id)->delete();
      }
    }
    $this->storeInvoiceInWorkOrder();
    Invoice::find($id)->delete();
    DB::commit();
    return Response::json(null);
  }
  public function cari_customer_list($company_id)
  {
    $condition = $company_id != 0 ? "company_id = $company_id" : '1=1';
    $data['item'] = Contact::where('is_pelanggan', 1)->whereRaw($condition)->select('id', 'name')->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function cari_jo($id)
  {
    // dd($id);
    $jo = JobOrder::with('commodity', 'trayek', 'trayek.from:id,name', 'trayek.to:id,name')->where('id', $id)->first();
    if (in_array($jo->service_type_id, [2, 3, 4])) {
      if ($jo->service_type_id == 4) {
        $imposition = 'BORONGAN';
      } elseif ($jo->service_type_id == 3) {
        $imposition = 'UNIT';
      } elseif ($jo->service_type_id == 2) {
        $imposition = 'KONTAINER';
      }
      $sql = "
        SELECT
          manifests.id,
          manifest_details.job_order_detail_id,
          manifest_details.customer_sj as dod_code,
          manifests.code,
          contacts.name as driver,
          vehicles.nopol,
          containers.container_no,
          vehicle_types.name as vname,
          services.account_sale_id,
          services.name as service,
          job_orders.shipment_date as shipment_date,
          commodities.name as commodity_name
        FROM
          manifests
          LEFT JOIN manifest_details ON manifest_details.header_id = manifests.id
          LEFT JOIN vehicles ON manifests.vehicle_id = vehicles.id
          LEFT JOIN containers ON manifests.container_id = containers.id
          LEFT JOIN contacts ON contacts.id = manifests.driver_id
          LEFT JOIN vehicle_types ON vehicle_types.id = manifests.vehicle_type_id
          LEFT JOIN job_order_details ON job_order_details.id = manifest_details.job_order_detail_id
          LEFT JOIN job_orders ON job_orders.id = job_order_details.header_id
          LEFT JOIN services ON services.id = job_orders.service_id
          LEFT JOIN commodities ON commodities.id = job_orders.commodity_id
        WHERE
          manifest_details.job_order_detail_id IN ( SELECT id FROM job_order_details WHERE header_id = $id )
          and manifests.is_invoice = 0
        GROUP BY
          manifests.id
        ";
      if ($jo->service_type_id == 4) {
        $sql = "
          SELECT
            manifests.id,
            manifest_details.job_order_detail_id,
            manifest_details.customer_sj as dod_code,
            manifests.code,
            contacts.name as driver,
            vehicles.nopol,
            containers.container_no,
            vehicle_types.name as vname,
            services.account_sale_id,
            services.name as service,
            job_orders.code,
            job_orders.shipment_date as shipment_date,
            commodities.name as commodity_name
          FROM
            manifests
            LEFT JOIN manifest_details ON manifest_details.header_id = manifests.id
            LEFT JOIN vehicles ON manifests.vehicle_id = vehicles.id
            LEFT JOIN containers ON manifests.container_id = containers.id
            LEFT JOIN contacts ON contacts.id = manifests.driver_id
            LEFT JOIN vehicle_types ON vehicle_types.id = manifests.vehicle_type_id
            LEFT JOIN job_order_details ON job_order_details.id = manifest_details.job_order_detail_id
            LEFT JOIN job_orders ON job_orders.id = job_order_details.header_id
            LEFT JOIN services ON services.id = job_orders.service_id
            LEFT JOIN commodities ON commodities.id = job_orders.commodity_id
            LEFT JOIN delivery_order_drivers ON manifests.id = delivery_order_drivers.manifest_id
          WHERE
            manifest_details.job_order_detail_id IN ( SELECT id FROM job_order_details WHERE header_id = $id )
            and manifests.is_invoice = 0
          GROUP BY
            job_orders.code
          ";
      }
      $man = DB::select($sql);
      // dd($man);
    } else if ($jo->service_type_id == 1) {
      /*
          Before Change
        */
      // $sql2="select
      // concat(null) as manifest_id,
      // concat(null) as job_order_detail_id,
      // concat(null) as manifest_detail_id,
      // concat(null) as driver,
      // concat(null) as nopol,
      // concat(null) as container_no,
      // vehicle_types.name as vname,
      // routes.name as trayek,
      // jo.no_po_customer,
      // jo.id as jo_id,
      // jo.code as codes,
      // jo.work_order_id as wo_id,
      // (select group_concat(distinct item_name) from job_order_details as jod1 where jod1.header_id = jo.id) as item_name,
      // Y.qty,
      // Y.price,
      // Y.total_price,
      // Y.imposition,
      // jo.description,
      // services.name as service
      // from job_orders as jo
      // left join services on jo.service_id = services.id
      // left join routes on jo.route_id = routes.id
      // left join vehicle_types on jo.vehicle_type_id = vehicle_types.id
      // left join (select if(imposition=1,volume,if(imposition=2,IF(weight > volumetric_weight, weight, volumetric_weight),qty)) as qty,header_id,imposition,price,total_price from job_order_details) Y on Y.header_id = jo.id
      // where jo.id = $id and jo.service_type_id = 1";
      // where jo.id = $id and jo.invoice_id is null and jo.service_type_id = 1"
      /*
          31-01-2022
        */
      $sql2 = "select
        manifests.id as manifest_id,
        md.customer_sj as dod_code,
        jod.id as job_order_detail_id,
        md.id as manifest_detail_id,
        driver.name as driver,
        vehicles.nopol as nopol,
        concat(null) as container_no,
        vehicle_types.name as vname,
        routes.name as trayek,
        jo.no_po_customer,
        jo.id as jo_id,
        jo.code as codes,
        jo.work_order_id as wo_id,
        (select group_concat(distinct item_name) from job_order_details as jod1 where jod1.header_id = jo.id) as item_name,
        Y.qty,
        (SELECT SUM(transported) FROM manifest_details JOIN manifests ON manifests.id = manifest_details.header_id WHERE manifest_details.job_order_detail_id = jod.id) AS transported_item,
        Y.price,
        Y.total_price,
        Y.imposition,
        jo.description,
        services.name as service,
        jo.shipment_date as shipment_date,
        commodities.name as commodity_name
        from job_orders as jo
        left join job_order_details as jod on jod.header_id = jo.id
        left join manifest_details as md on md.job_order_detail_id = jod.id
        left join manifests on manifests.id = md.header_id
        left join contacts as driver on driver.id = manifests.driver_id
        left join vehicles on vehicles.id = manifests.vehicle_id
        left join services on jo.service_id = services.id
        left join routes on jo.route_id = routes.id
        left join vehicle_types on jo.vehicle_type_id = vehicle_types.id
        left join commodities ON commodities.id = jo.commodity_id
        left join (select if(imposition=1,volume,if(imposition=2,IF(weight > volumetric_weight, weight, volumetric_weight),qty)) as qty,header_id,imposition,price,total_price from job_order_details) Y on Y.header_id = jo.id
        where jo.id = $id and jo.service_type_id = 1
        group by jo.id";
      $man = DB::select($sql2);
      // dd($man);
    } elseif (in_array($jo->service_type_id, [6, 7])) {
      $man = JobOrderDetail::leftJoin('job_orders', 'job_orders.id', '=', 'job_order_details.header_id')
        ->leftJoin('services', 'services.id', '=', 'job_orders.service_id')
        ->leftJoin('pieces', 'pieces.id', '=', 'job_orders.piece_id')
        ->where("job_order_details.header_id", $id)
        ->select(
          'job_orders.code',
          'job_orders.no_po_customer',
          'job_orders.id',
          'job_orders.description',
          'job_orders.total_price',
          'job_orders.price',
          'job_order_details.id as job_order_detail_id',
          'job_order_details.qty',
          'job_order_details.item_name',
          'pieces.name as piece_name',
          'services.name as service'
        )
        ->first();
    } elseif (in_array($jo->service_type_id, [12, 13, 14, 15])) {
      $jo = JobOrder::with('service')->leftJoin('services', 'services.id', 'service_id')->where('job_orders.id', $id)->selectRaw('job_orders.*, services.service_type_id AS service_type_id')->first();
      if ($jo->service_type_id == 14) {
        $data['service'] = DB::table('packagings')->where('job_order_id', $id)->first();
      } else if ($jo->service_type_id == 15) {
        $data['service'] = DB::table('warehouserents')->where('job_order_id', $id)->selectRaw('warehouserents.*, DATEDIFF(end_date, start_date) AS durasi')->first();
      } else if ($jo->service_type_id == 12) {
        $data['service'] = DB::table('handlings')->where('job_order_id', $id)->first();
      } else if ($jo->service_type_id == 13) {
        $data['service'] = DB::table('stuffings')->where('job_order_id', $id)->first();
      }
      if (in_array($jo->service_type_id, [1, 2, 3, 4, 5, 6, 7])) {
        $man = JobOrderDetail::leftJoin('job_orders', 'job_orders.id', '=', 'job_order_details.header_id')
          ->leftJoin('services', 'services.id', '=', 'job_orders.service_id')
          ->leftJoin('pieces', 'pieces.id', '=', 'job_orders.piece_id')
          ->where("job_order_details.header_id", $id)
          ->select(
            'job_orders.code',
            'job_orders.no_po_customer',
            'job_order_details.id',
            'job_orders.description',
            'job_order_details.total_price',
            'job_order_details.price',
            'job_order_details.id as job_order_detail_id',
            'job_order_details.qty',
            'job_order_details.item_name',
            'job_order_details.imposition',
            'pieces.name as piece_name',
            'services.name as service'
          )
          ->get();
      } else {
        $existingBorongan = JobOrderDetail::whereHeaderId($id)
          ->whereImposition(4)
          ->count('id');
        if ($existingBorongan < 1) {
          $man = JobOrderDetail::leftJoin('job_orders', 'job_orders.id', '=', 'job_order_details.header_id')
            ->leftJoin('services', 'services.id', '=', 'job_orders.service_id')
            ->leftJoin('pieces', 'pieces.id', '=', 'job_orders.piece_id')
            ->where("job_order_details.header_id", $id)
            ->select(
              'job_orders.code',
              'job_orders.no_po_customer',
              'job_order_details.id',
              'job_orders.description',
              'job_order_details.total_price',
              'job_order_details.price',
              'job_order_details.id as job_order_detail_id',
              DB::raw('IF(job_order_details.imposition = 1, job_order_details.volume, IF(job_order_details.imposition = 2, job_order_details.weight, job_order_details.qty)) AS qty'),
              'job_order_details.item_name',
              'job_order_details.imposition',
              'pieces.name as piece_name',
              'services.name as service'
            )
            ->get();
        } else {
          $man = JobOrderDetail::leftJoin('job_orders', 'job_orders.id', '=', 'job_order_details.header_id')
            ->leftJoin('services', 'services.id', '=', 'job_orders.service_id')
            ->leftJoin('pieces', 'pieces.id', '=', 'job_orders.piece_id')
            ->where("job_order_details.header_id", $id)
            ->select(
              'job_orders.code',
              'job_orders.no_po_customer',
              'job_order_details.id',
              'job_orders.description',
              DB::raw('SUM(job_order_details.total_price) AS total_price'),
              DB::raw('SUM(job_order_details.price) AS price'),
              'job_order_details.id as job_order_detail_id',
              DB::raw('1 AS qty'),
              DB::raw('GROUP_CONCAT(job_order_details.item_name) AS item_name'),
              DB::raw('4 AS imposition'),
              'pieces.name as piece_name',
              'services.name as service'
            )
            ->get();
        }
      }
    } else {
      return Response::json(['message' => 'Error Has Found!'], 500);
    }
    $data['jo'] = $jo;
    $data['manifest'] = $man;
    $data['imposition'] = $imposition ?? '-';
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function cari_wo(Request $request, $id)
  {
    $wo = WorkOrder::find($id);
    $data = [];
    if ($wo->is_job_packet == 0) {
      if (($wo->quotation->bill_type ?? null) == 2) {
        // jika quotation borongan
        $stta = $wo->quotation;
        $jos = DB::table('job_orders')->where('work_order_id', $wo->id)->first();
        $data[] = [
          'job_order_id' => $jos->id,
          'job_order_detail_id' => null,
          'no_po_customer' => '-',
          'trayek' => '-',
          'origin' => '-',
          'destination' => '-',
          'commodity' => $wo->name,
          'cost_type_id' => null,
          'work_order_id' => $id,
          'price' => $stta->price_full_contract,
          'total_price' => ($wo->qty * $stta->price_full_contract),
          'imposition' => 1,
          'qty' => $wo->qty,
          'description' => 'Borongan',
          'is_other_cost' => 0,
          'type_other_cost' => 0,
          'manifest_id' => null,
          'imposition_name' => $stta->imposition_name,
          'vehicle_type_name' => '-',
          'code' => $wo->code,
          'driver' => '-',
          'nopol' => '-',
          'container_no' => '-',
        ];
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
      }
      $wr = "1=1";
      if ($request->jo_list_append) {
        $wr .= " AND job_orders.id NOT IN ($request->jo_list_append)";
      }
      $jor = JobOrder::whereRaw("work_order_id = $id and service_type_id != 4 and $wr")->get();
      foreach ($jor as $key => $jo) {
        if (in_array($jo->service_type_id, [2, 3])) {
          if ($jo->service_type_id == 3) {
            $imposition = 'UNIT';
          } elseif ($jo->service_type_id == 2) {
            $imposition = 'KONTAINER';
          }
          $sql = "
                SELECT
                  manifests.id,
                  manifest_details.job_order_detail_id,
                  job_order_details.header_id as job_order_id,
                  manifests.code,
                  contacts.name as driver,
                  vehicles.nopol,
                  vehicle_types.name as vname,
                  services.name as service
                FROM
                  manifests
                  LEFT JOIN manifest_details ON manifest_details.header_id = manifests.id
                  LEFT JOIN job_order_details ON manifest_details.job_order_detail_id = job_order_details.id
                  LEFT JOIN job_orders ON job_orders.id = job_order_details.header_id
                  LEFT JOIN services ON services.id = job_orders.service_id
                  LEFT JOIN vehicles ON manifests.vehicle_id = vehicles.id
                  LEFT JOIN contacts ON contacts.id = manifests.driver_id
                  LEFT JOIN vehicle_types ON vehicle_types.id = manifests.vehicle_type_id
                WHERE
                  manifest_details.job_order_detail_id IN ( SELECT id FROM job_order_details WHERE header_id = $jo->id )
                  AND job_orders.service_type_id IN (2,3)
                GROUP BY
                  manifests.id
              ";
          $man = DB::select($sql);
          // dd($man);
          foreach ($man as $mas) {
            $data[] = [
              'job_order_id' => $jo->id,
              'job_order_detail_id' => $mas->job_order_detail_id,
              'no_po_customer' => $jo->no_po_customer,
              'trayek' => $jo->trayek->name ?? '-',
              'origin' => $jo->trayek->from->name ?? '-',
              'destination' => $jo->trayek->to->name ?? '-',
              'commodity' => $mas->service,
              'cost_type_id' => null,
              'work_order_id' => $id,
              'price' => $jo->price,
              'total_price' => (in_array($jo->service_type_id, [2, 3]) ? ($jo->price) : $jo->total_price),
              'imposition' => 1,
              'qty' => 1,
              'description' => $jo->description ?? '-',
              'is_other_cost' => 0,
              'type_other_cost' => 0,
              'manifest_id' => $mas->id,
              'imposition_name' => ($jo->service_type_id == 2 ? 'Kontainer' : 'Unit'),
              'vehicle_type_name' => $mas->vname,
              'code' => $mas->code,
              'driver' => $mas->driver ?? '-',
              'nopol' => $mas->nopol ?? '-',
              'container_no' => '-',
            ];
          }
        } else if ($jo->service_type_id == 1 || $jo->service_type_id == 12 || $jo->service_type_id == 13 || $jo->service_type_id == 15) {
          // $detail=JobOrderDetail::where('header_id', $jo->id)->get();
          $stt = [
            1 => 'Kubikasi',
            2 => 'Tonase',
            3 => 'Item',
          ];
          $sql2 = "
              select
              routes.name as trayek,
              origin.name as origin,
              destination.name as destination,
              jo.no_po_customer,
              jo.id as jo_id,
              jo.code as code_jo,
              jo.work_order_id as wo_id,
              (select group_concat(distinct item_name) from job_order_details as jod1 where jod1.header_id = jo.id) as item_name,
              Y.qty,
              Y.price,
              Y.total_price,
              Y.imposition,
              jo.description,
              services.name as service
              from job_orders as jo
              left join services on services.id = jo.service_id
              left join routes on jo.route_id = routes.id
              left join cities as origin on origin.id = routes.from
              left join cities as destination on destination.id = routes.to
              left join (select sum(if(imposition=1,volume,if(imposition=2,weight,qty))) as qty,header_id,imposition,sum(total_price) as total_price,max(price) as price from job_order_details group by header_id) Y on Y.header_id = jo.id
              where jo.id = {$jo->id} and (jo.service_type_id = 1 || jo.service_type_id = 12 || jo.service_type_id = 13 || jo.service_type_id = 15)";
          // where jo.id = {$jo->id} and jo.invoice_id is null and (jo.service_type_id = 1 || jo.service_type_id = 12 || jo.service_type_id = 13 || jo.service_type_id = 15)";
          $man = DB::select($sql2);
          // dd($man);
          foreach ($man as $mas) {
            $data[] = [
              'job_order_id' => $mas->jo_id,
              'no_po_customer' => $mas->no_po_customer,
              'job_order_detail_id' => null,
              'trayek' => $mas->trayek,
              'origin' => $mas->origin,
              'destination' => $mas->destination,
              'commodity' => $mas->service,
              'cost_type_id' => null,
              'work_order_id' => $id,
              'price' => $mas->price,
              'total_price' => $mas->total_price,
              'imposition' => $mas->imposition,
              'imposition_name' => $stt[$mas->imposition],
              'qty' => $mas->qty,
              'description' => $mas->description ?? '-',
              'is_other_cost' => 0,
              'type_other_cost' => 0,
              'manifest_id' => null,
              'vehicle_type_name' => null,
              'code' => $mas->code_jo,
              'driver' => '-',
              'nopol' => '-',
              'container_no' => '-',
            ];
          }
          // return Response::json($data,500,[],JSON_NUMERIC_CHECK);
        } elseif (in_array($jo->service_type_id, [6, 7])) {
          $mas = DB::table('job_order_details')
            ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('pieces', 'pieces.id', 'job_orders.piece_id')
            ->where("job_orders.id", $jo->id)
            // ->whereIn('job_orders.service_type_id',[6,7])
            ->selectRaw('
                    job_order_details.total_price,
                    job_order_details.price,
                    job_order_details.id as job_order_detail_id,
                    job_order_details.qty,
                    job_order_details.item_name,
                    job_orders.code,
                    job_orders.no_po_customer,
                    job_orders.id,
                    job_orders.description,
                    pieces.name as piece_name,
                    services.name as service
                  ')
            ->first();
          // dd($man);
          $data[] = [
            'job_order_id' => $mas->id,
            'job_order_detail_id' => $mas->job_order_detail_id,
            'no_po_customer' => $mas->no_po_customer,
            'trayek' => $mas->item_name,
            'origin' => '-',
            'destination' => '-',
            'commodity' => $mas->service,
            'cost_type_id' => null,
            'work_order_id' => $id,
            'price' => $mas->price,
            'total_price' => $mas->total_price,
            'imposition' => 1,
            'imposition_name' => $mas->piece_name,
            'qty' => $mas->qty,
            'description' => $mas->description ?? '-',
            'is_other_cost' => 0,
            'type_other_cost' => 0,
            'manifest_id' => null,
            'vehicle_type_name' => '-',
            'code' => $mas->code,
            'driver' => '-',
            'nopol' => '-',
            'container_no' => '-',
          ];
        } else {
          continue;
        }
      }
    } else {
      $jobOrder = DB::table('job_orders')
        ->whereWorkOrderId($wo->id)
        ->first();
      $grandtotal = DB::table('job_packets')
        ->join('work_order_details', 'work_order_details.id', 'job_packets.work_order_detail_id')
        ->where('work_order_details.header_id', $wo->id)
        ->sum('total_price');
      $jobPackets = DB::table('job_packets')
        ->join('work_order_details', 'work_order_details.id', 'job_packets.work_order_detail_id')
        ->where('work_order_details.header_id', $id)
        ->select('job_packets.id', 'job_packets.work_order_detail_id', 'work_order_details.price_list_id', 'work_order_details.quotation_detail_id', 'job_packets.qty', 'job_packets.duration', 'job_packets.price', 'job_packets.total_price')
        ->orderBy('job_packets.total_price', 'desc')
        ->get();
      $data = [];
      foreach ($jobPackets as $unit) {
        if ($unit->price_list_id) {
          $priceList = DB::table('price_lists')
            ->whereId($unit->price_list_id)
            ->select('service_id', 'handling_type')
            ->first();
          $service_id = $priceList->service_id;
        } else {
          $quotationDetail = DB::table('quotation_details')
            ->whereId($unit->quotation_detail_id)
            ->select('service_id', 'handling_type')
            ->first();
          $service_id = $quotationDetail->service_id;
        }
        $service = DB::table('services')
          ->whereId($service_id)
          ->first();
        $service_name = $service->name;
        if ($unit->total_price > 0) {
          $qty = $unit->qty * $unit->duration;
          if ($qty == 0) {
            $qty = 1;
          }
          $param =  [
            'work_order_id' => $id,
            'job_order_id' => $jobOrder->id,
            'commodity' => $service_name,
            'is_ppn' => $service->is_ppn,
            'job_order_detail_id' => null,
            'imposition_name' => 'Paket',
            'qty' => $qty,
            'price' => $unit->price,
            'total_price' => $unit->total_price,
            'trayek' => null,
            'code' => $wo->code,
            'no_po_customer' => null,
            'container_no' => null,
            'cost_type_id' => null,
            'is_other_cost' => 0,
            'type_other_cost' => 0,
            'manifest_id' => null,
            'driver' => null
          ];
          if ($service->is_ppn) {
            $data[] = $param;
          } else {
            array_unshift($data, $param);
          }
        }
      }
    }
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function cari_default_akun()
  {
    $data = AccountDefault::first();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function cari_wo_collectible($id)
  {
    // $sql="SELECT work_orders.id FROM work_orders LEFT JOIN job_orders on job_orders.work_order_id = work_orders.id"
  }
  public function cancel_posting($id)
  {
    DB::beginTransaction();
    $status = 200;
    $message = "success";
    //check closing
    $closing = $this->check_closing_date("invoice");
    if ($closing == 1) {
      $message = "Invoice sudah di closing";
      $status = 422;
      return Response::json(['message' => $message], $status);
    }
    $i = Invoice::find($id);
    $i->increment('qty_batal_posting', 1);
    $journal_id = $i->journal_id;
    CashTransaction::where('type_transaction_id', 26)->where('relation_id', $i->id)->delete();
    Receivable::where('type_transaction_id', 26)->where('relation_id', $i->id)->delete();
    $i->update([
      'journal_id' => null,
      'status' => 2
    ]);
    Journal::where('id', $journal_id)->delete();
    DB::commit();
    return Response::json(['message' => $message], $status);
  }
  /*
      Date : 09-03-2020
      Description : Mem-posting invoice jual ke jurnal dan memberikan
                    faktur pajak kepada invoice
      Developer : Didin
      Status : Edit
    */
  public function posting(Request $request, $id)
  {
    $request->validate([
      // 'journal_date' => 'required'
    ], [
      'Tanggal posting Jurnal tidak boleh kosong'
    ]);
    DB::beginTransaction();
    $account_defaults = DB::table('account_defaults')
      ->first();
    // if(($account_defaults->piutang ?? null) === null) {
    //     return Response::json(['message' => 'Akun piutang pada setting default akun masih belum di-setting. Silahkan lakukan setting akun piutang pada Setting / Keuangan / Setting Akun'], 421);
    // }
    // $ppn_count = DB::table('invoice_details')
    // ->whereHeaderId($id)
    // ->sum('ppn');
    $ppn_count = DB::table('invoice_taxes')
      ->whereHeaderId($id)
      ->sum('amount');
    if ($ppn_count > 0) {
      $tax_invoice_count = DB::table('tax_invoices')
        ->whereNull('invoice_id')
        ->whereRaw('DATE_FORMAT(NOW(), "%Y-%m-%d") >= start_date AND DATE_FORMAT(NOW(), "%Y-%m-%d") <= expiry_date')
        ->count('id');
      // if($tax_invoice_count == 0) {
      //     return Response::json(['message' => 'Belum ada faktur pajak yang tersedia'], 421);
      // }
      if ($tax_invoice_count != 0) {
        $latest_tax = DB::table('tax_invoices')
          ->whereNull('invoice_id')
          ->whereRaw('DATE_FORMAT(NOW(), "%Y-%m-%d") >= start_date AND DATE_FORMAT(NOW(), "%Y-%m-%d") <= expiry_date')
          ->orderBy('id', 'ASC')
          ->first();
        DB::table('tax_invoices')
          ->whereId($latest_tax->id)
          ->update([
            'invoice_id' => $id
          ]);
      }
    }
    $in = DB::table('invoices')
      ->leftJoin(DB::raw('(select sum(amount) as taxs, header_id from invoice_taxes group by header_id) ix'), 'ix.header_id', 'invoices.id')
      ->leftJoin(DB::raw('(select sum(total_price+ppn-discount) as total_price, header_id from invoice_details group by header_id) idd'), 'idd.header_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('
      invoices.*,
      sum(idd.total_price+ix.taxs) as grand_total
      ')
      ->groupBy('invoices.id')
      ->first();
    // dd($in);
    $j = Journal::create([
      'company_id' => $in->company_id,
      'type_transaction_id' => 26, //invoice
      'relation_id' => $in->id,
      'date_transaction' => Carbon::parse($request->journal_date),
      'created_by' => auth()->id(),
      'code' => $in->code,
      'description' => 'Pendapatan atas Invoice No. ' . $in->code,
      'status' => 2
    ]);
    JournalDetail::where('header_id', $j->id)->delete();
    $sql_invoice_jo = "
      SELECT
        IF(work_orders.is_job_packet != 1, services.account_sale_id, direct_services.account_sale_id) AS account_sale_id,
        IF(work_orders.is_job_packet != 1, services.id, direct_services.id) as service_id,
        CONCAT('layanan ', IF(work_orders.is_job_packet != 1, services.name, direct_services.name)) AS name,
        job_orders.code,
        sum( invoice_details.total_price ) AS total,
        sum(ix.taxs+invoice_details.ppn) as ppn,
        invoice_details.job_order_id,
        invoice_details.job_order_detail_id,
        invoice_details.discount as discount
      FROM
        invoice_details
        LEFT JOIN job_orders ON job_orders.id = invoice_details.job_order_id
        LEFT JOIN work_orders ON work_orders.id = job_orders.work_order_id
        LEFT JOIN services ON services.id = job_orders.service_id
        LEFT JOIN services AS direct_services ON direct_services.id = invoice_details.service_id
        LEFT JOIN (select sum(amount) as taxs, invoice_detail_id from invoice_taxes group by invoice_detail_id) ix on ix.invoice_detail_id = invoice_details.id
      WHERE invoice_details.header_id = $id and invoice_details.cost_type_id is null
      GROUP BY
        invoice_details.id
      ";
    $sql_invoice_ct = "
      SELECT
        cost_types.akun_kas_hutang,
        cost_types.akun_uang_muka,
        cost_types.akun_biaya,
        cost_types.name,
        sum( invoice_details.total_price ) AS total,
        sum(ix.taxs+invoice_details.ppn) as ppn,
        invoice_details.discount as discount,
        invoice_details.job_order_id
      FROM
        invoice_details
        LEFT JOIN cost_types ON cost_types.id = invoice_details.cost_type_id
        LEFT JOIN (select sum(amount) as taxs, invoice_detail_id from invoice_taxes group by invoice_detail_id) ix on ix.invoice_detail_id = invoice_details.id
      WHERE
        invoice_details.header_id = $id
        AND invoice_details.cost_type_id is not null
      GROUP BY
        invoice_details.id
      ";
    $sql_tax_inline = "
      SELECT
        taxes.pemotong_pemungut,
        taxes.akun_penjualan,
        taxes.name,
        job_orders.code,
        sum( amount ) AS total
      FROM
        invoice_taxes
        LEFT JOIN taxes ON taxes.id = invoice_taxes.tax_id
        LEFT JOIN invoice_details ON invoice_details.id = invoice_taxes.invoice_detail_id
        LEFT JOIN job_orders ON job_orders.id = invoice_details.job_order_id
      WHERE
        invoice_taxes.tax_id IS NOT NULL
        AND invoice_details.header_id = $id
      GROUP BY
        invoice_taxes.tax_id
      ";
    $ket = 'JO';
    $diskon = InvoiceDetail::where('header_id', $id)->sum('discount');
    $ppn = InvoiceDetail::where('header_id', $id)->sum('ppn');
    $invoice_jo = DB::select($sql_invoice_jo);
    $invoice_jo = collect($invoice_jo)->map(function ($val) {
      if ($val->job_order_id && $val->job_order_detail_id) {
        if (SalesOrder::hasJobOrder($val->job_order_id)) {
          $ket = 'SO';
          $jod = JOD::show($val->job_order_detail_id);
          $item = Item::show($jod->item_id);
          $val->account_sale_id = $item->account_sale_id;
          $val->name = $item->name;
        }
      }
      return $val;
    })->toArray();
    $invoice_ct = DB::select($sql_invoice_ct);
    // dd($invoice_jo);
    $tax = DB::select($sql_tax_inline);
    if ($in->type_bayar == 1) {
      $code = new TransactionCode($in->company_id, 'cashIn');
      $code->setCode();
      $trx_code = $code->getCode();
      $account = Account::find($in->account_cash_id);
      $i = CashTransaction::create([
        'company_id' => $in->company_id,
        'type_transaction_id' => 26,
        'relation_id' => $in->id,
        'code' => $trx_code,
        'reff' => $in->code,
        'jenis' => 1,
        'type' => $account->no_cash_bank,
        'description' => 'Pendapatan Invoice ' . $in->code,
        'total' => 0,
        'account_id' => $in->account_cash_id,
        'date_transaction' => Carbon::parse($request->journal_date),
        'status_cost' => 1,
        'created_by' => auth()->id(),
        'journal_id' => $j->id
      ]);
      CashTransaction::where('relation_id', $i->id)->delete();
    } else {
      $i = Receivable::create([
        'company_id' => $in->company_id,
        'contact_id' => $in->customer_id,
        'type_transaction_id' => 26,
        'journal_id' => $j->id,
        'relation_id' => $in->id,
        'created_by' => auth()->id(),
        'code' => $in->code,
        'date_transaction' => Carbon::parse($request->journal_date),
        'date_tempo' => Carbon::parse($in->date_invoice)->addDays($in->termin),
        'description' => 'Pendapatan Invoice No. ' . $in->code
      ]);
      ReceivableDetail::where('header_id', $i->id)->delete();
      ReceivableDetail::create([
        'header_id' => $i->id,
        'journal_id' => $j->id,
        'type_transaction_id' => 26,
        'relation_id' => $in->id,
        'code' => $in->code,
        'date_transaction' => Carbon::parse($request->journal_date),
      ]);
      Invoice::find($in->id)->update([
        'receivable_id' => $i->id
      ]);
    }
    $is_error = false;
    $total_in_jo = 0;
    foreach ($invoice_jo as $key => $value) {
      // if (($value->account_sale_id ?? null) === null) {
      //   $err_message="Akun pendapatan / penjualan untuk $value->name belum ditentukan!<br>";
      //   $is_error=true;
      //   return Response::json(['message' => $err_message], 421);
      // }
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $value->account_sale_id ?? null,
        'credit' => $value->total,
        'description' => 'Pendapatan Invoice ' . $in->code . ' untuk ' . $ket . ' ' . $value->code . ' layanan ' . $value->name,
      ]);
      if ($in->type_bayar == 1) {
        CashTransactionDetail::create([
          'header_id' => $i->id,
          'account_id' => $value->account_sale_id ?? null,
          'contact_id' => $in->customer_id,
          'amount' => ($value->total + $value->ppn - $value->discount),
          'description' => 'Pendapatan Invoice ' . $in->code . ' untuk ' . $ket . ' ' . $value->code . ' layanan ' . $value->name,
        ]);
        CashTransaction::find($i->id)->update([
          'total' => DB::raw('total+' . ($value->total + $value->ppn - $value->discount))
        ]);
      } else {
        ReceivableDetail::whereRaw("header_id = $i->id")->update([
          'debet' => DB::raw('debet+' . ($value->total + $value->ppn - $value->discount))
        ]);
      }
      $total_in_jo += $value->total;
    }
    $total_in_ct = 0;
    foreach ($invoice_ct as $key => $value) {
      // if (empty($account_defaults->pendapatan_reimburse)) {
      //   $err_message="- Akun Default Pendapatan Reimburse belum ditentukan!<br>";
      //   $is_error=true;
      //   return Response::json(['message' => $err_message], 421);
      //   continue;
      // }
      if ($value->job_order_id) {
        // JIKA DARI REIMBURSE
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $account_defaults->pendapatan_reimburse ?? null,
          'credit' => $value->total,
          'description' => 'Pendapatan Invoice ' . $in->code . ' untuk jenis biaya ' . $value->name,
        ]);
      } else {
        // JIKA BIAYA TAMBAHAN INVOICE
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $value->akun_biaya ?? null,
          'credit' => $value->total,
          'description' => 'Pendapatan Invoice ' . $in->code . ' untuk jenis biaya ' . $value->name,
        ]);
      }
      if ($in->type_bayar == 1) {
        CashTransactionDetail::create([
          'header_id' => $i->id,
          'account_id' => $value->akun_kas_hutang ?? null,
          'contact_id' => $in->customer_id,
          'amount' => ($value->total + $value->ppn - $value->discount),
          'description' => 'Pendapatan Invoice ' . $in->code . ' untuk jenis biaya ' . $value->name,
        ]);
      } else {
        ReceivableDetail::whereRaw("header_id = $i->id")->update([
          'debet' => DB::raw('debet+' . ($value->total + $value->ppn - $value->discount))
        ]);
      }
      $total_in_ct += $value->total;
    }
    $total_taxes = 0;
    foreach ($tax as $key => $value) {
      // if (empty($value->akun_penjualan)) {
      //   $err_message="- Akun Penjualan untuk pajak $value->name belum ditentukan!<br>";
      //   $is_error=true;
      //   return Response::json(['message' => $err_message], 421);
      //   continue;
      // }
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $value->akun_penjualan ?? null,
        'debet' => ($value->pemotong_pemungut == 1 ? abs($value->total) : 0),
        'credit' => ($value->pemotong_pemungut == 2 ? abs($value->total) : 0),
        'description' => 'PPn ' . $value->name . ' untuk Invoice ' . $in->code . ' No. JO ' . $value->code,
      ]);
      if ($value->pemotong_pemungut == 1) {
        $total_taxes += abs($value->total);
      } else {
        $total_taxes -= abs($value->total);
      }
    }
    //kondisi dan jurnal diskon
    if ($diskon > 0) {
      $dp = AccountDefault::first();
      // if (!$dp->diskon_penjualan) {
      //   $err_message="- Akun default Diskon Penjualan belum ditentukan!<br>";
      //   $is_error=true;
      //   return Response::json(['message' => $err_message], 421);
      // }
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $dp->diskon_penjualan ?? null,
        'debet' => $diskon,
        'description' => 'Diskon atas Invoice ' . $in->code,
      ]);
    }
    //kondisi dan jurnal ppn
    if ($ppn > 0) {
      $dp = AccountDefault::first();
      // if (!$dp->ppn_out) {
      //   $err_message="Akun default PPN Keluaran belum ditentukan!<br>";
      //   return Response::json(['message'=> $err_message], 421);
      //   $is_error=true;
      // }
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $dp->ppn_out ?? null,
        'credit' => $ppn,
        'description' => 'PPN atas Invoice ' . $in->code,
      ]);
    }
    //jurnal kas / hutang
    $total_kas_hutang = $total_in_jo + $total_in_ct + $total_taxes - $diskon - $total_taxes;
    if ($in->type_bayar == 1) {
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $in->account_cash_id ?? null,
        'debet' => $in->grand_total,
        'description' => 'Pendapatan atas Invoice ' . $in->code,
      ]);
    } else {
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $account_defaults->piutang ?? null,
        'debet' => $in->grand_total,
        'description' => 'Pendapatan atas Invoice ' . $in->code,
      ]);
    }
    //jurnal biaya ----------------------------------------------<<
    // $sql_cost="
    // SELECT
    //  SUM(job_order_costs.total_price) as total,
    //  CONCAT(cost_types.name,' - No. JO',job_orders.code) as name,
    //  cost_types.akun_biaya,
    //  cost_types.akun_uang_muka
    // FROM
    //  job_order_costs
    //  LEFT JOIN job_orders ON job_orders.id = job_order_costs.header_id
    //  LEFT JOIN invoice_details on job_orders.id = invoice_details.job_order_id
    //  LEFT JOIN cost_types on job_order_costs.header_id = cost_types.id
    //  WHERE invoice_details.header_id = $id
    // GROUP BY job_orders.id, cost_types.id
    // ";
    // $biaya=DB::select($sql_cost);
    // $jj=Journal::create([
    //   'company_id' => $in->company_id,
    //   'type_transaction_id' => 26, //invoice
    //   'date_transaction' => Carbon::parse($request->journal_date),
    //   'created_by' => auth()->id(),
    //   'code' => $in->code,
    //   'description' => 'Biaya atas invoice '.$in->code,
    // ]);
    // foreach ($biaya as $key => $value) {
    //   JournalDetail::create([
    //     'header_id' => $jj->id,
    //     'account_id' => $value->akun_biaya,
    //     'debet' => $value->total,
    //     'description' => 'Biaya '.$value->name,
    //   ]);
    //   JournalDetail::create([
    //     'header_id' => $jj->id,
    //     'account_id' => $value->akun_uang_muka,
    //     'credit' => $value->total,
    //     'description' => 'Biaya '.$value->name,
    //   ]);
    // }
    //end jurnal biaya---------------------------------------------->>
    Invoice::find($in->id)->update([
      'status' => 3,
      'journal_id' => $j->id
    ]);
    if ($is_error) {
      return Response::json(['message' => $err_message], 500); // ada pesan error
    }
    DB::commit();
  }
  public function approve($id)
  {
    DB::beginTransaction();
    Invoice::find($id)->update([
      'approve_by' => auth()->id(),
      'is_approve' => 1,
      'date_approve' => Carbon::now(),
      'status' => 2
    ]);
    DB::commit();
  }
  public function print(Request $request, $id)
  {
    /*
        1 => 'INVOICE WO GABUNGAN',
        2 => 'INVOICE WO PERSATUAN',
        3 => 'INVOICE INTER ISLAND',
        4 => 'INVOICE TRUCKING',
        5 => 'INVOICE PROJECT',
      */
    $format = $request->format;
    $i = Invoice::find($id);
    if ($i->printed_amount == null) {
      $i->printed_amount = '[0, 0, 0, 0, 0]';
      $i->save();
    }
    $printed_amount = json_decode($i->printed_amount);
    $printed_amount[$format - 1] += 1;
    $i->printed_amount = json_encode($printed_amount);
    $i->save();
    if ($format == 1) {
      $response = $this->print_format_1($id);
    } else if ($format == 2) {
      $response = $this->print_format_2($id, 0, $request->show_ppn);
    } else if ($format == 3) {
      $response = $this->print_format_3($id, 0, $request->show_ppn);
    } else if ($format == 4) {
      $response = $this->print_format_4($id, 0, $request->show_ppn);
    } else if ($format == 5) {
      $response = $this->print_format_5($id, $request->show_ppn);
    }
    return $response;
  }
  public function print_format_1($id)
  {
    $data['item'] = $i;
    $data['details'] = InvoiceDetail::where('header_id', $id)
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->selectRaw('
        invoice_details.*,
        min(job_orders.code) as code_inv,
        job_orders.no_bl as bl,
        count(job_orders.work_order_id) as totalJO,
        sum(if(job_orders.service_type_id in (1,2,3),invoice_details.total_price,0)) as trucking2,
        sum(if(job_orders.service_type_id not in (1,2,3),invoice_details.total_price,0)) as custom_all2,
        sum(invoice_details.ppn)+(select sum(amount) from invoice_taxes as itx where itx.invoice_detail_id = invoice_details.id) as total_ppn,
        (
          select sum(jo_customAll.total_price)
          from job_orders as jo_customAll
          where jo_customAll.work_order_id = job_orders.work_order_id
          and jo_customAll.service_type_id not in (1,2,3)
        ) as custom_all,
        (
          select sum(jo_trucking.total_price)
          from job_orders as jo_trucking
          where jo_trucking.work_order_id = job_orders.work_order_id
          and jo_trucking.service_type_id in (1,2,3)
        ) as trucking
      ')
      ->groupBy('job_orders.work_order_id')
      ->get();
    // dd($data);
    $data['remark'] = $this->remark;
    return PDF::loadView('pdf.invoice.wo-gabungan', $data)
      ->stream();
  }
  public function print_format_2($id, $is_gabungan = 0, $show_ppn)
  {
    $i = Invoice::with('customer:id,name', 'company:id,name')->whereId($id)->firstOrFail();
    $printed_amount = json_decode($i->printed_amount) ?? [];
    $data['label'] = ($printed_amount[1] ?? 1) == 1 ? 'ASLI' : 'COPY';
    $data['item'] = $i;
    $wolist = DB::table('invoice_details')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->where('header_id', $id)
      ->where('work_orders.id', '!=', null)
      ->selectRaw('job_orders.work_order_id,work_orders.code as wo_code')
      ->groupBy('job_orders.work_order_id')
      ->get();
    $ppn_total = DB::table('invoice_taxes AS IT')
      ->leftJoin('taxes AS T', 'T.id', 'IT.tax_id')
      ->leftJoin('invoice_details AS ID', 'ID.id', 'IT.invoice_detail_id') // tidak link ke IT.invoice_detail_id
      ->where('T.is_ppn', 1)
      ->where('IT.header_id', $id)
      ->sum('IT.amount');
    $data['ppn_total'] = $ppn_total;
    $other_tax = DB::table('invoice_taxes AS IT')
      ->leftJoin('taxes AS T', 'T.id', 'IT.tax_id')
      ->leftJoin('invoice_details AS ID', 'ID.id', 'IT.invoice_detail_id') // tidak link ke IT.invoice_detail_id
      ->where('T.is_ppn', 0)
      ->where('IT.header_id', $id)
      ->sum('IT.amount');
    $data['other_tax'] = $other_tax;
    $data['details'] = [];
    foreach ($wolist as $key => $value) {
      // dd($value);
      if (!$value->work_order_id) {
        continue;
      }
      $addon = DB::select("select group_concat(distinct job_orders.aju_number) as aju,group_concat(distinct job_orders.no_bl) as bl, GROUP_CONCAT(distinct work_orders.code) as code_wo,concat(max(job_orders.vessel_name),'  ',max(job_orders.voyage_no)) as vessel from invoice_details left join job_orders on job_orders.id = invoice_details.job_order_id left join work_orders on work_orders.id = job_orders.work_order_id where work_orders.id = $value->work_order_id")[0];
      $trucking = DB::table('invoice_details')
        ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
        ->leftJoin('services', 'services.id', 'job_orders.service_id')
        ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
        ->leftJoin('container_types', 'container_types.id', 'job_orders.container_type_id')
        ->leftJoin('vehicle_types', 'vehicle_types.id', 'job_orders.vehicle_type_id')
        ->whereRaw("job_orders.service_type_id in (1,2,3) and header_id = $id and job_orders.work_order_id = $value->work_order_id and invoice_details.cost_type_id is null")
        ->selectRaw("sum(invoice_details.total_price) as total_price, sum(invoice_details.qty) as qty,
        services.name as service,routes.name as trayek, sum(invoice_details.ppn) as ppn, concat(container_types.code) as ctype, vehicle_types.name as vtype, invoice_details.commodity_name")
        ->groupBy('job_orders.service_id', 'job_orders.route_id', 'job_orders.container_type_id', 'job_orders.vehicle_type_id')
        ->orderBy('job_orders.work_order_detail_id', 'asc')
        ->get();
      $ff = DB::table('invoice_details')
        ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
        ->leftJoin('services', 'services.id', 'job_orders.service_id')
        ->leftJoin('pieces', 'pieces.id', 'job_orders.piece_id')
        ->whereRaw("job_orders.service_type_id not in (1,2,3) and header_id = $id and job_orders.work_order_id = $value->work_order_id and invoice_details.cost_type_id is null")
        ->selectRaw("
        invoice_details.commodity_name,
        invoice_details.price as price,
        sum(invoice_details.total_price) as total_price,
        sum(invoice_details.qty) as qty,services.name as service,
        sum(invoice_details.ppn) as ppn,
        sum(invoice_details.discount) as discount,
        pieces.name as piece
        ")
        ->groupBy('services.id', 'job_orders.piece_id')
        ->orderBy('job_orders.work_order_detail_id', 'asc')
        // ->orderBy('services.id','desc')
        ->get();
      $data['pol_pod'] = DB::select("select
        max(cstart.name) as pol,
        max(cend.name) as pod,
        max(vessels.name) as vessel,
        max(voyage_schedules.voyage) as voyage
        from invoice_details
        left join job_orders on job_orders.id = invoice_details.job_order_id
        left join job_order_details on job_order_details.header_id = job_orders.id
        left join manifest_details on manifest_details.job_order_detail_id = job_order_details.id
        left join manifests on manifests.id = manifest_details.header_id
        left join containers on containers.id = manifests.container_id
        left join voyage_schedules on voyage_schedules.id = containers.voyage_schedule_id
        left join vessels on vessels.id = voyage_schedules.vessel_id
        left join cities as cstart on cstart.id = voyage_schedules.pol_id
        left join cities as cend on cend.id = voyage_schedules.pod_id
        ")[0];
      // dd($ff);
      $data['details'][$key]['detail_trucking'] = $trucking;
      $data['details'][$key]['detail_ff'] = $ff;
      $data['details'][$key]['work_order_code'] = $value->wo_code;
      $data['details'][$key]['addon'] = $addon;
      if ($key == 0) {
        $lain = DB::table('invoice_details')
          ->leftJoin('cost_types', 'cost_types.id', 'invoice_details.cost_type_id')
          ->whereRaw("header_id = $id and cost_type_id is not null")
          ->selectRaw('
          cost_types.id,
          cost_types.name,
          sum(invoice_details.total_price) as total_price,
          invoice_details.ppn
          ')
          ->groupBy('invoice_details.cost_type_id')
          ->get();
        // dd($lain);
        if (count($lain) > 0) {
          $data['details'][$key]['detail_other'] = $lain;
        }
      }
    }
    if ($is_gabungan) {
      return $data;
    }
    if ($ppn_total <= 0) {
      $ppn_total_persen = 0;
    } else {
      $ppn_total_persen = round($ppn_total / $i->sub_total * 100 ?? 1);
    }
    if ($other_tax <= 0) {
      $other_tax_persen = 0;
    } else {
      $other_tax_persen = round(($other_tax / $i->sub_total ?? 1) * 100);
    }
    $data['show_ppn'] = $show_ppn ?? 0;
    $data['remark'] = $this->remark;
    $data['ppn_persen'] = $ppn_total_persen;
    $data['other_tax_persen'] = $other_tax_persen;
    $customPaper = 'A5'; //[0,0,210,297];
    // return view('pdf.invoice.custom-invoice', $data);//->setPaper('A5','landscape')->stream('Invoice.pdf');
    return PDF::loadView('pdf.invoice.custom-invoice', $data)->setPaper($customPaper, 'landscape')->stream('Invoice.pdf');
  }
  public function print_format_3_bkp($id)
  {
    $data['item'] = Invoice::find($id);
    $data['details'] = DB::table('invoice_details')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->leftJoin('services', 'services.id', 'job_orders.service_id')
      ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
      ->leftJoin('pieces', 'pieces.id', 'job_orders.piece_id')
      ->where('invoice_details.header_id', $id)
      ->whereIn('job_orders.service_type_id', [1, 2, 3])
      ->selectRaw('
        invoice_details.*,
        job_orders.work_order_id,
        services.name as service,
        routes.name as route,
        pieces.name as piece,
        count(job_orders.work_order_id) as totalJO,
        (select group_concat(distinct wos.code) from invoice_details as ids left join job_orders as jos on jos.id = ids.job_order_id left join work_orders as wos on wos.id = jos.work_order_id where ids.header_id = invoice_details.id) as no_wo,
        sum(invoice_details.ppn) as ppn
      ')
      ->groupBy('job_orders.work_order_id')
      ->get();
    $data['container'] = DB::table('manifest_details')
      ->leftJoin('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
      ->leftJoin('invoice_details', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('containers', 'containers.id', 'manifests.container_id')
      ->leftJoin('container_types', 'container_types.id', 'job_orders.container_type_id')
      ->whereRaw("invoice_details.header_id = $id")
      ->selectRaw('concat(container_types.code) as container, count(distinct manifests.id) as total')
      ->groupBy('containers.id')->get();
    $data['tax'] = DB::table('invoice_taxes')->leftJoin('taxes', 'taxes.id', 'invoice_taxes.tax_id')->where('invoice_taxes.header_id', $id)->selectRaw('taxes.name, sum(invoice_taxes.amount) as amount')->groupBy('invoice_taxes.header_id')->first();
    // dd($data);
    // hitung container
    $container = 0;
    $data['remark'] = $this->remark;
    foreach ($data['details'] as $key => $detail) {
      if (!empty($detail->job_order->detail)) {
        foreach ($detail->job_order->detail as $jod) {
          if (!empty($jod->manifest->container)) {
            $container++;
          }
        }
      }
    }
    if ($container <= 10) {
      return PDF::loadView('pdf.invoice.inter-island-1', $data)
        ->setPaper('a5')
        ->stream();
    } else {
      return PDF::loadView('pdf.invoice.inter-island-2', $data)
        ->setPaper('a5')
        ->stream();
    }
  }
  public function print_format_3($id, $is_gabungan = 0, $show_ppn)
  {
    $i = Invoice::find($id);
    $printed_amount = json_decode($i->printed_amount);
    $data['label'] = $printed_amount[2] == 1 ? 'ASLI' : 'COPY';
    $data['item'] = $i;
    // $data['details']=InvoiceDetail::leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
    // ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
    // ->where('invoice_details.header_id', $id)
    // ->whereIn('job_orders.service_type_id', [1,2,3])
    // ->selectRaw('
    //   invoice_details.*, job_orders.work_order_id,
    //   count(job_orders.work_order_id) as totalJO
    // ')
    // ->groupBy('job_orders.work_order_id')
    // ->get();
    $data['details'] = DB::table('invoice_details')
      ->leftJoin('manifests', 'manifests.id', 'invoice_details.manifest_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
      ->leftJoin('cities as city_start', 'city_start.id', 'routes.city_from')
      ->leftJoin('cities as city_end', 'city_end.id', 'routes.city_to')
      ->leftJoin('container_types', 'container_types.id', 'job_orders.container_type_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'job_orders.vehicle_type_id')
      ->leftJoin('services', 'services.id', 'job_orders.service_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->whereRaw("invoice_details.header_id = $id and invoice_details.job_order_id is not null and invoice_details.cost_type_id is null")
      ->selectRaw('
      sum(if(job_orders.service_type_id=1,job_orders.total_unit,invoice_details.qty)) as qty,
      sum(invoice_details.total_price) as total_price,
      sum(invoice_details.ppn) as ppn,
      if(job_orders.service_type_id=1,sum(invoice_details.total_price),invoice_details.price) as price,
      vehicle_types.name as vname,
      container_types.code as cname,
      group_concat(distinct job_orders.id) as jo_list,
      if(vehicle_types.id is not null,vehicle_types.name,container_types.code) as name,
      CONCAT(services.name,"<br>",routes.name) as trayek,
      city_start.name as city_start,
      city_end.name as city_end,
      group_concat(distinct work_orders.code SEPARATOR \'<br>\') as code_wo,
      manifests.vehicle_type_id,
      manifests.container_type_id
      ')
      ->groupBy('manifests.vehicle_type_id')
      ->get();
    $data['po'] = DB::table('job_orders as jo')
      ->leftJoin('invoice_details as invd', 'invd.job_order_id', 'jo.id')
      ->selectRaw('group_concat(distinct jo.no_po_customer) as po_customer')
      ->where('invd.header_id', $id)
      ->groupBy('jo.id')
      ->first();
    $data['additional'] = DB::table('invoice_details')
      ->leftJoin('cost_types', 'cost_types.id', 'invoice_details.cost_type_id')
      ->whereRaw("invoice_details.header_id = $id and cost_types.id is not null")
      ->selectRaw('cost_types.name, invoice_details.total_price')->get();
    $data['manifests'] = DB::table('invoice_details')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_orders.id', 'job_order_details.header_id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers', 'manifests.id', 'delivery_order_drivers.manifest_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
      ->leftJoin('cities as city_start', 'city_start.id', 'routes.city_from')
      ->leftJoin('cities as city_end', 'city_end.id', 'routes.city_to')
      ->leftJoin('container_types', 'container_types.id', 'manifests.container_type_id')
      ->leftJoin('vehicles', 'vehicles.id', 'delivery_order_drivers.vehicle_id')
      ->leftJoin('containers', 'containers.id', 'manifests.container_id')
      ->leftJoin('contacts as driver', 'driver.id', 'delivery_order_drivers.driver_id')
      ->whereRaw("invoice_details.header_id = $id and invoice_details.manifest_id is not null")
      ->selectRaw('
      container_types.code as name,
      invoice_details.price,
      containers.container_no,
      (select GROUP_CONCAT(jbdr.item_name) from manifest_details as mds left join job_order_details as jbdr on jbdr.id = mds.job_order_detail_id where mds.header_id = manifests.id) as item_name,
      (select MAX(jbdr.weight) from manifest_details as mds left join job_order_details as jbdr on jbdr.id = mds.job_order_detail_id where mds.header_id = manifests.id) as tonase,
      (select if(SUM(mds.transported)=0,1,SUM(mds.transported)) from manifest_details as mds where mds.header_id = manifests.id) as qty,
      IF(driver.id is not null,driver.name,delivery_order_drivers.driver_name) as driver,
      group_concat(distinct job_orders.no_po_customer) as po_customer,
      delivery_order_drivers.code as no_sj,
      city_start.name as city_start,
      city_end.name as city_end,
      IF(vehicles.id is not null,vehicles.nopol,delivery_order_drivers.nopol) as nopol,
      work_orders.code as code_wo
      ')
      ->groupBy('manifests.id')->get();
    $data['tax'] = DB::table('invoice_taxes')->leftJoin('taxes', 'taxes.id', 'invoice_taxes.tax_id')->where('invoice_taxes.header_id', $id)->selectRaw('taxes.name, sum(invoice_taxes.amount) as amount')->groupBy('invoice_taxes.header_id')->first();
    // hitung container
    // $container = 0;
    // foreach ($data['manifests'] as $key => $manifest) {
    //   for ($i=0; $i < 100; $i++) {
    //     $data['manifests'][] = $manifest;
    //   }
    // }
    // dd($data);
    if ($is_gabungan) {
      return $data;
    }
    $data['show_ppn'] = $show_ppn ?? 0;
    $data['remark'] = $this->remark;
    return PDF::loadView('pdf.invoice.inter-island-rev', $data)
      ->setPaper('a5')
      ->stream();
  }
  public function print_format_4($id, $is_gabungan = 0, $show_ppn)
  {
    $i = Invoice::find($id);
    $printed_amount = json_decode($i->printed_amount) ?? [];
    $data['label'] = ($printed_amount[3] ?? 1) == 1 ? 'ASLI' : 'COPY';
    $data['item'] = $i;
    // $data['details']=InvoiceDetail::leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
    // ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
    // ->where('invoice_details.header_id', $id)
    // ->whereIn('job_orders.service_type_id', [1,2,3])
    // ->selectRaw('
    //   invoice_details.*, job_orders.work_order_id,
    //   count(job_orders.work_order_id) as totalJO
    // ')
    // ->groupBy('job_orders.work_order_id')
    // ->get();
    $data['details'] = DB::table('invoice_details')
      ->leftJoin('manifests', 'manifests.id', 'invoice_details.manifest_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
      ->leftJoin('cities as city_start', 'city_start.id', 'routes.city_from')
      ->leftJoin('cities as city_end', 'city_end.id', 'routes.city_to')
      ->leftJoin('container_types', 'container_types.id', 'job_orders.container_type_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'job_orders.vehicle_type_id')
      ->leftJoin('services', 'services.id', 'job_orders.service_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->whereRaw("invoice_details.header_id = $id and invoice_details.job_order_id is not null and invoice_details.cost_type_id is null")
      ->selectRaw('
      sum(if(job_orders.service_type_id!=1,1,invoice_details.qty)) as qty,
      sum(invoice_details.total_price) as total_price,
      sum(invoice_details.ppn) as ppn,
      invoice_details.price,
      vehicle_types.name as vname,
      container_types.code as cname,
      job_orders.service_type_id,
      group_concat(distinct job_orders.id) as jo_list,
      if(job_orders.service_type_id=1,invoice_details.imposition_name,if(vehicle_types.id is not null,vehicle_types.name,container_types.code)) as name,
      CONCAT(services.name,"<br>",routes.name) as trayek,
      city_start.name as city_start,
      city_end.name as city_end,
      group_concat(distinct work_orders.code SEPARATOR \'<br>\') as code_wo,
      manifests.vehicle_type_id,
      manifests.container_type_id
      ')
      ->groupBy('job_orders.service_id')
      ->get();
    $data['po'] = DB::table('job_orders')
      ->leftJoin('invoice_details', 'invoice_details.job_order_id', 'job_orders.id')
      ->selectRaw('group_concat(distinct job_orders.no_po_customer) as po_customer')
      ->where('invoice_details.header_id', $id)
      ->groupBy('job_orders.id')
      ->first();
    $data['additional'] = DB::table('invoice_details')
      ->leftJoin('cost_types', 'cost_types.id', 'invoice_details.cost_type_id')
      ->whereRaw("invoice_details.header_id = $id and cost_types.id is not null")
      ->selectRaw('cost_types.name, invoice_details.total_price')->get();
    $data['manifests'] = DB::table('invoice_details')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_orders.id', 'job_order_details.header_id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers', 'manifests.id', 'delivery_order_drivers.manifest_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
      ->leftJoin('cities as city_start', 'city_start.id', 'routes.city_from')
      ->leftJoin('cities as city_end', 'city_end.id', 'routes.city_to')
      ->leftJoin('container_types', 'container_types.id', 'manifests.container_type_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('vehicles', 'vehicles.id', 'delivery_order_drivers.vehicle_id')
      ->leftJoin('contacts as driver', 'driver.id', 'delivery_order_drivers.driver_id')
      ->whereRaw("invoice_details.header_id = $id and invoice_details.manifest_id is not null")
      ->selectRaw('
      if(vehicle_types.id is not null,vehicle_types.name,container_types.code) as name,
      invoice_details.price,
      (select GROUP_CONCAT(jbdr.item_name) from manifest_details as mds left join job_order_details as jbdr on jbdr.id = mds.job_order_detail_id where mds.header_id = manifests.id) as item_name,
      (select MAX(jbdr.weight) from manifest_details as mds left join job_order_details as jbdr on jbdr.id = mds.job_order_detail_id where mds.header_id = manifests.id) as tonase,
      (select if(SUM(mds.transported)=0,1,SUM(mds.transported)) from manifest_details as mds where mds.header_id = manifests.id) as qty,
      IF(driver.id is not null,driver.name,delivery_order_drivers.driver_name) as driver,
      group_concat(distinct job_orders.no_po_customer) as po_customer,
      delivery_order_drivers.code as no_sj,
      city_start.name as city_start,
      city_end.name as city_end,
      IF(vehicles.id is not null,vehicles.nopol,delivery_order_drivers.nopol) as nopol,
      work_orders.code as code_wo
      ')
      ->groupBy('manifests.id')->get();
    $data['tax'] = DB::table('invoice_taxes')->leftJoin('taxes', 'taxes.id', 'invoice_taxes.tax_id')->where('invoice_taxes.header_id', $id)->selectRaw('taxes.name, sum(invoice_taxes.amount) as amount')->groupBy('invoice_taxes.header_id')->first();
    // hitung container
    // $container = 0;
    // foreach ($data['manifests'] as $key => $manifest) {
    //   for ($i=0; $i < 100; $i++) {
    //     $data['manifests'][] = $manifest;
    //   }
    // }
    // dd($data);
    if ($is_gabungan) {
      return $data;
    }
    // return view('pdf.invoice.trucking2',$data);
    $data['show_ppn'] = $show_ppn ?? 0;
    $data['remark'] = $this->remark;
    return PDF::loadView('pdf.invoice.trucking2', $data)
      ->setPaper('a5')
      ->stream();
  }
  public function print_format_5($id, $show_ppn)
  {
    $i = Invoice::find($id);
    $printed_amount = json_decode($i->printed_amount);
    $data['label'] = $printed_amount[4] == 1 ? 'ASLI' : 'COPY';
    $data['item'] = $i;
    $data['details'] = InvoiceDetail::leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->where('invoice_details.header_id', $id)
      ->selectRaw('
        invoice_details.*, job_orders.work_order_id,
        count(job_orders.work_order_id) as totalJO
      ')
      ->groupBy('job_orders.work_order_id')
      ->get();
    $data['wo'] = DB::table('invoice_details')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->where('invoice_details.header_id', $id)
      ->selectRaw('
      group_concat(distinct work_orders.code) as wo_code
      ')
      ->first();
    // dd($data);
    // hitung container
    $container = 0;
    foreach ($data['details'] as $key => $detail) {
      if (!empty($detail->job_order->detail)) {
        foreach ($detail->job_order->detail as $jod) {
          if (!empty($jod->manifest->container)) {
            $container++;
          }
        }
      }
    }
    $data['show_ppn'] = $show_ppn ?? 0;
    $data['remark'] = $this->remark;
    return PDF::loadView('pdf.invoice.project', $data)
      ->setPaper('a5')
      ->stream();
  }
  public function cari_jo_cost(Request $request)
  {
    // dd($request);
    $input = $request->input();
    $jo_list = "0";
    foreach ($input as $key => $value) {
      if (empty($value)) {
        continue;
      } elseif (empty($value['job_order_id'])) {
        continue;
      }
      $jo_list .= ',' . $value['job_order_id'];
    }
    // dd($jo_list);
    $data = DB::table('job_order_costs')
      ->leftJoin('job_orders', 'job_orders.id', '=', 'job_order_costs.header_id')
      ->leftJoin('cost_types', 'cost_types.id', '=', 'job_order_costs.cost_type_id')
      ->whereRaw("job_order_costs.type = 2 and job_order_costs.header_id in ($jo_list)")
      ->select([
        'job_order_costs.cost_type_id as id',
        'job_order_costs.qty',
        'job_order_costs.price',
        'job_order_costs.total_price',
        'job_order_costs.vendor_id',
        'job_order_costs.header_id as job_order_id',
        'cost_types.name as name',
        'job_orders.code as parent'
      ])->get();
    // dd($data);
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function cari_invoice(Request $request)
  {
    $invoice_list = DB::table('invoices')->leftJoin('contacts', 'contacts.id', 'invoices.customer_id')
      // ->groupBy('invoices.customer_id')
      // ->selectRaw('contacts.id,contacts.name,group_concat(invoices.id) as invoice_list')
      ->selectRaw('invoices.id')
      ->where('contacts.id', $request->customer_id)
      ->get()
      ->pluck('id');
    $data = DB::table('invoices')
      ->leftJoin(DB::raw('(select invoice_details.header_id, group_concat(distinct jo.aju_number) as aju, group_concat(distinct jo.no_bl) as bl, group_concat(distinct jo.no_po_customer) as po_customer from invoice_details left join job_orders as jo on jo.id = invoice_details.job_order_id group by invoice_details.header_id) as Y'), 'Y.header_id', 'invoices.id')
      // ->whereRaw("invoices.id in ($request->invoice_list)")
      ->whereIn("invoices.id", $invoice_list)
      ->selectRaw("
      id,
      code,
      Y.aju,
      Y.bl,
      Y.po_customer,
      grand_total,
      date_invoice,
      IF(printed_amount IS NOT NULL, 1, 0) AS is_printed
      ")
      ->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function list_invoice($id)
  {
    $data = DB::table('invoices')
      ->where('customer_id', $id)
      ->selectRaw('id, code')
      ->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function list_city_origin($id)
  {
    $data = DB::table('invoices')
      ->leftJoin('invoice_details', 'invoice_details.header_id', 'invoices.id')
      ->leftJoin('manifests', 'manifests.id', 'invoice_details.manifest_id')
      ->leftJoin('routes', 'routes.id', 'manifests.route_id')
      ->leftJoin('cities as origin', 'origin.id', 'routes.city_from')
      ->leftJoin('cities as destination', 'destination.id', 'routes.city_to')
      ->where('invoices.customer_id', $id)
      ->selectRaw('distinct origin.id as origin_id, origin.name as origin_name')
      ->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function list_city_destination($id)
  {
    $data = DB::table('invoices')
      ->leftJoin('invoice_details', 'invoice_details.header_id', 'invoices.id')
      ->leftJoin('manifests', 'manifests.id', 'invoice_details.manifest_id')
      ->leftJoin('routes', 'routes.id', 'manifests.route_id')
      ->leftJoin('cities as destination', 'destination.id', 'routes.city_to')
      ->where('invoices.customer_id', $id)
      ->selectRaw('distinct destination.id as destination_id, destination.name as destination_name')
      ->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function print_wo_gabungan(Request $request)
  {
    // dd($request);
    $request->validate([
      'list' => 'required',
      'type_wo' => 'required'
    ]);
    $data['item'] = Invoice::whereRaw("id in ($request->list)")->first();
    $lastSerial = DB::table('invoice_joins')->groupBy('serial')->selectRaw('serial')->orderBy('serial', 'desc')->first();
    // dd($lastSerial);
    $exp = explode(",", $request->list);
    DB::beginTransaction();
    foreach ($exp as $key => $value) {
      $serial = ($lastSerial ? $lastSerial->serial : 0);
      $i = Invoice::find($value);
      if ($i->printed_amount == null) {
        $i->printed_amount = '[0, 0, 0, 0, 0]';
        $i->save();
      }
      if ($i->id == $exp[0]) {
        $code = $i->code;
      }
      InvoiceJoin::create([
        'serial' => $serial + 1,
        'invoice_id' => $value,
        'type_wo' => $request->type_wo
      ]);
      $i->update([
        'code' => $code . '-' . ($key + 1),
        'is_join' => 1,
      ]);
    }
    $data['details'] = InvoiceDetail::whereRaw("header_id in ($request->list)")
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
      ->leftJoin('invoices', 'invoices.id', 'invoice_details.header_id')
      ->selectRaw('
        invoice_details.*,
        min(invoices.code) as code_invoice,
        job_orders.no_bl as bl,
        count(job_orders.work_order_id) as totalJO,
        group_concat(distinct job_orders.no_po_customer) as po_customer,
        sum(if(invoice_details.job_order_id is null,invoice_details.total_price,0)) as reimburse,
        sum(if(job_orders.service_type_id in (1,2,3),invoice_details.total_price,0)) as trucking2,
        sum(if(job_orders.service_type_id not in (1,2,3),invoice_details.total_price,0)) as custom_all2,
        sum(invoice_details.ppn)+(select sum(amount) from invoice_taxes as itx where itx.invoice_detail_id = invoice_details.id) as total_ppn,
        (
          select sum(jo_customAll.total_price)
          from job_orders as jo_customAll
          where jo_customAll.work_order_id = job_orders.work_order_id
          and jo_customAll.service_type_id not in (1,2,3)
        ) as custom_all,
        (
          select sum(jo_trucking.total_price)
          from job_orders as jo_trucking
          where jo_trucking.work_order_id = job_orders.work_order_id
          and jo_trucking.service_type_id in (1,2,3)
        ) as trucking
      ')
      ->groupBy('invoice_details.header_id')
      ->get();
    $data['type_wo'] = $request->type_wo;
    DB::commit();
    if ($request->type_wo == 1) {
      foreach ($data['details'] as $key => $value) {
        $data['lampiran'][$key] = $this->print_format_2($value->header_id, 1, 0);
      }
    } else {
      foreach ($data['details'] as $key => $value) {
        $data['lampiran'][$key] = $this->print_format_4($value->header_id, 1, 0);
      }
    }
    // dd($data);
    $data['remark'] = $this->remark;
    return PDF::loadView('pdf.invoice.wo-gabungan', $data)
      ->stream();
  }
  public function jo_list(Request $request)
  {
    $data = DB::table('job_orders')
      ->leftJoin('services', 'services.id', 'job_orders.service_id')
      ->where('customer_id', $request->customer_id)
      ->selectRaw("
      job_orders.id,
      concat(job_orders.code,' / ',services.name) as name,
      job_orders.code
      ")
      ->orderBy('job_orders.code', 'desc')->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  /*
      Date : 01-09-2020
      Description : Menyimpan invoice pada work order
      Developer : Didin
      Status : Create
    */
  public function storeInvoiceInWorkOrder()
  {
    $invoices = DB::table('invoice_details')
      ->join('work_orders', 'work_orders.id', 'invoice_details.work_order_id')
      ->whereNull('work_orders.invoice_id')
      ->select('invoice_details.header_id AS invoice_id', 'work_orders.id AS work_order_id')
      ->get();
    foreach ($invoices as $invoice) {
      DB::table('work_orders')
        ->whereId($invoice->work_order_id)
        ->update([
          'invoice_id' => $invoice->invoice_id
        ]);
    }
    DB::table('work_orders')
      ->whereRaw('id NOT IN (SELECT work_order_id FROM invoice_details WHERE work_order_id IS NOT NULL)')
      ->update([
        'invoice_id' => null
      ]);
  }
  /*
      Date : 20-11-2021
      Description : Date Closing
      Developer : Atika
      Status : Create
    */
  public function check_closing_date($slug)
  {
    $status = 0;
    $month = new Carbon();
    $startPeriode = $month->copy()->startOfMonth()->format('Y-m-d');
    $endPeriode = $month->copy()->endOfMonth()->format('Y-m-d');
    $closing = Closing::where('start_periode', $startPeriode)->where('end_periode', $endPeriode)->where('status', 1)->where('company_id', auth()->user()->company_id)->first();
    $typeTransaction = TypeTransaction::where('is_lock', true)->where('slug', $slug)->whereBetween('last_date_lock', [$startPeriode, $endPeriode])->first();
    if (!empty($typeTransaction) && !empty($closing)) {
      $status = 1;
    }
    return $status;
  }
  public function print_rekap_invoice($id, $name, $start, $end, $status, $route, $no_jo, $origin, $destination)
  {
    $company = DB::table('companies')->where('id', auth()->user()->company_id)->first();
    $customer = DB::table('contacts')->where('id', $id)->first();
    $invoice = Invoice::where('customer_id', $id)->get();
    $id_invoice = $invoice->pluck('id')->toArray();
    $detail = InvoiceDetail::whereIn('invoice_details.header_id', $id_invoice)
      ->leftJoin('invoices', 'invoices.id', 'invoice_details.header_id')
      ->leftJoin('companies', 'companies.id', 'invoices.company_id')
      ->leftJoin('contacts', 'contacts.id', 'invoices.customer_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to')
      ->leftJoin('delivery_order_drivers', 'delivery_order_drivers.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'delivery_order_drivers.vehicle_id');
    // $detail= InvoiceDetail::whereIn('invoice_details.header_id',$id_invoice)
    // ->leftJoin('invoices','invoices.id','invoice_details.header_id')
    // ->leftJoin('companies','companies.id','invoices.company_id')
    // ->leftJoin('contacts','contacts.id','invoices.customer_id')
    // ->leftJoin('job_orders', 'job_orders.work_order_id', 'invoice_details.work_order_id')
    // ->leftJoin('job_order_details','job_order_details.header_id','job_orders.id')
    // ->leftJoin('manifest_details','manifest_details.job_order_detail_id','job_order_details.id')
    // ->leftJoin('manifests','manifests.id','manifest_details.header_id')
    // ->leftJoin('delivery_order_drivers','delivery_order_drivers.manifest_id','manifests.id')
    // ->leftJoin('vehicles','vehicles.id','delivery_order_drivers.vehicle_id');
    if ($start != "1" && $end != "1") {
      $startDate = date("Y-m-d", strtotime($start));
      $endDate = date("Y-m-d", strtotime($end));
      $data['start_date'] = $startDate;
      $data['end_date'] = $endDate;
      $detail = $detail->whereBetween('job_orders.shipment_date', [$startDate, $endDate]);
    } else {
      $data['start_date'] = '-';
      $data['end_date'] = '-';
    }
    if ($status != "0") {
      $detail = $detail->where('invoices.status', (int)$status);
    }
    if ($route != 'null') {
      // dd($detail->get(),(int)$route);
      // $route= DB::table('routes')->where('id',$route)->first();
      // dd($route);
      // dd($detail->pluck('manifests.route_id')->toArray());
      // $detail= $detail->where('manifests.route_id',(int)$route);
      $detail = $detail->where('r.id', (int)$route);
    }
    if ($no_jo != "0" && $no_jo != "null") {
      $detail = $detail->where('invoices.id', (int)$no_jo);
    }
    if ($origin != "0" && $origin != "null") {
      $detail = $detail->where('from.id', (int)$origin);
    }
    if ($destination != "0" && $destination != "null") {
      $detail = $detail->where('to.id', (int)$destination);
    }
    $detail = $detail->groupBy('job_orders.id');
    $detail = $detail->selectRaw('
        invoice_details.*,
        invoices.code as no_invoice,
        invoices.date_invoice as date_invoice,
        companies.name as company_name,
        contacts.name as contact_name,
        contacts.id as contact_id,
        job_orders.id as jo_id,
        job_order_details.id as jo_detail_id,
        job_orders.code as code_jo,
        job_orders.shipment_date as jo_shipment,
        vehicles.nopol as nopol,
        manifest_details.customer_sj as sj_code,
        manifest_details.customer_po as po_code,
        manifest_details.customer_receipt as gr_code,
        manifests.route_id as manifest_route_id,
        job_order_details.item_name as item_name,
        job_order_details.item_id as item_id,
        job_order_details.qty as item_qty,
        concat(job_order_details.item_id,"-",job_order_details.qty) as item_data_id,
        concat(job_order_details.item_name,"-",invoice_details.qty) as item_name_qty
        ')
      ->where('item_name', '!=', '')
      ->orderBy('jo_shipment', 'asc')
      ->orderBy('sj_code', 'asc')
      ->get();
    $data['company'] = $company;
    $data['customer'] = $customer;
    $data['invoice'] = $invoice;
    $data['template'] = $name;
    $data['detail'] = $detail;
    $customPaper = array(0, 0, 297, 210); // kertas A4
    if ($name == 3) {
      return PDF::loadView('pdf.rekap_invoice_per_nopol', $data)
        ->setPaper('A4', 'landscape')
        ->stream('Rekap Invoice.pdf');
    } else if ($name == 2) {
      // $data['detail'] = collect($detail)->groupBy('manifest_route_id');
      $data['detail'] = collect($detail)->groupBy(function ($item) {
        return $item['item_name'] . '-' . $item['item_qty'];
      });
      return PDF::loadView('pdf.rekap_invoice_per_produk', $data)
        ->setPaper('A4')
        ->stream('Rekap Invoice.pdf');
    } else if ($name == 1) {
      $detail_gr = $detail;
      // $detail_gr= $detail->where('gr_code','!=',NULL);
      // $detail_no_gr= $detail->where('gr_code',NULL);
      // $data['detail_with_gr'] = collect($detail_gr)->groupBy('manifest_route_id');
      $data['detail_with_gr'] = collect($detail_gr)->sortBy('jo_shipment');
      // $data['detail_with_gr'] = collect($detail_gr)->groupBy(function ($item) {
      //     return $item['item_name'] . '-' . $item['item_qty'];
      // });
      // $data['detail'] = collect($detail)->groupBy('manifest_route_id');
      $data['detail'] = collect($detail)->sortBy('jo_shipment');
      // $data['detail'] = collect($detail)->groupBy(function ($item) {
      //     return $item['item_name'] . '-' . $item['item_qty'];
      // });
      return PDF::loadView('pdf.rekap_invoice', $data)
        ->setPaper('A4')
        ->stream('Rekap Invoice.pdf');
      // return view('pdf.rekap_invoice',compact('data'));
    }
  }
  public function print_rekap_sj($id, $name, $start, $end)
  {
    $company = DB::table('companies')->where('id', auth()->user()->company_id)->first();
    $customer = DB::table('contacts')->where('id', $id)->first();
    $invoice = Invoice::where('status', 3)->where('customer_id', $id)->get();
    $id_invoice = $invoice->pluck('id')->toArray();
    // $detail= InvoiceDetail::whereIn('invoice_details.header_id',$id_invoice)
    // ->leftJoin('invoices','invoices.id','invoice_details.header_id')
    // ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
    // ->leftJoin('job_order_details','job_order_details.header_id','job_orders.id')
    // ->leftJoin('manifest_details','manifest_details.job_order_detail_id','job_order_details.id')
    // ->leftJoin('manifests','manifests.id','manifest_details.header_id')
    // ->leftJoin('delivery_order_drivers','delivery_order_drivers.manifest_id','manifests.id')
    // ->leftJoin('vehicles','vehicles.id','delivery_order_drivers.vehicle_id')
    // ->leftJoin('routes', 'routes.id', 'manifests.route_id')
    // ->leftJoin('cities AS c_to', 'c_to.id', 'routes.city_to')
    // ->leftJoin('cities AS c_from', 'c_from.id', 'routes.city_from')
    // ->leftJoin('contacts AS d', 'd.id', 'manifests.driver_id')
    // ->leftJoin('contacts AS h', 'h.id', 'manifests.helper_id')
    // ->selectRaw('
    //   invoice_details.*,
    //   invoices.date_invoice as date_invoice,
    //   job_orders.id as jo_id,
    //   job_order_details.id as jo_detail_id,
    //   job_orders.code as code_jo,
    //   vehicles.nopol as nopol,
    //   manifests.customer_sj as sj_code,
    //   c_to.name AS city_to,
    //   c_from.name AS city_from,
    //   d.name AS driver,
    //   h.name AS helper,
    //   job_order_details.item_name as item_name,
    //   job_order_details.qty as item_qty')
    // ->get();
    $startDate = date("Y-m-d H:i:s", strtotime($start));
    $endDate = date("Y-m-d H:i:s", strtotime($end));
    $JO = DB::table('job_order_details')
      ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
      ->where('job_orders.customer_id', $id)
      ->select('job_order_details.id as id')
      ->get();
    $id_jo_detail = $JO->pluck('id')->toArray();
    $detail = DB::table('manifest_details')->whereIn('manifest_details.job_order_detail_id', $id_jo_detail)
      ->join('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
      ->leftJoin('pieces', 'pieces.id', 'job_order_details.piece_id')
      ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
      ->leftJoin('vehicles', 'vehicles.id', 'manifests.vehicle_id')
      ->leftJoin('routes', 'routes.id', 'manifests.route_id')
      ->leftJoin('cities AS c_to', 'c_to.id', 'routes.city_to')
      ->leftJoin('cities AS c_from', 'c_from.id', 'routes.city_from')
      ->leftJoin('contacts AS d', 'd.id', 'manifests.driver_id')
      ->leftJoin('contacts AS h', 'h.id', 'manifests.helper_id')
      ->leftJoin('contacts AS c', 'c.id', 'job_orders.customer_id')
      ->whereBetween('manifests.date_manifest', [$startDate, $endDate])
      ->groupBy('manifests.id')
      ->selectRaw('
        job_orders.id as jo_id,
        job_order_details.id as jo_detail_id,
        manifests.date_manifest,
        manifests.id as manifest_id,
        manifest_details.id as manifest_detail_id,
        job_orders.code as code_jo,
        vehicles.nopol as nopol,
        c.name AS customer_name,
        c.id AS cutomer_id,
        manifest_details.customer_sj as sj_code,
        c_to.name AS city_to,
        c_to.id AS city_to_id,
        c_from.name AS city_from,
        d.name AS driver,
        h.name AS helper,
        job_order_details.item_name as item_name,
        job_order_details.qty as item_qty,
        job_order_details.price as item_price,
        job_order_details.total_price,
        job_orders.customer_id,
        routes.id as route_id,
        concat(c_to.name,"-",job_order_details.item_name,"-",job_order_details.qty) as item_data_id,
        pieces.name as piece_name
      ')
      ->get();
    // dd($detail);
    $ca_qty = count($detail);
    $collect_detail = collect($detail)->groupBy('item_data_id');
    $priceData = 0;
    foreach ($detail as $dd) {
      $countJ = DB::table('job_order_details')
        ->where('job_order_details.item_name', $dd->item_name)
        ->where('job_order_details.qty', $dd->item_qty)
        ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
        ->where('job_orders.customer_id', $dd->customer_id)
        ->select('job_order_details.id as id')
        ->get();
      $id = $countJ->pluck('id')->toArray();
      $countJA = DB::table('manifest_details')->whereIn('job_order_detail_id', $id)->get();
      $cp = count($countJA);
      // dd($cp);
      $priceData += $dd->item_qty * ($dd->item_price * $cp);
    }
    // dd($priceData);
    $total = 0;
    $total_qty = 0;
    // dd(count($collect_detail));
    // dd($collect_detail);
    $detail_item = $collect_detail->map(function ($s) {
      foreach ($s as $value) {
        // dd($value);
        return [
          'count' => count($s),
          'qty' => $value->item_qty,
          'price' => $value->item_price,
          'item_name' => $value->item_name,
          'piece_name' => $value->piece_name,
          'tujuan' => $value->city_to,
          'amount_all' => $value->item_price * $value->item_qty * count($s),
          'customer_id' => $value->customer_id,
          'customer_name' => $value->customer_name
        ];
      }
    });
    // dd($stok);
    $totalData = 0;
    foreach ($detail_item as $s) {
      // dd($s['price']);
      $totalData += $s['price'] * $s['qty'] * $s['count'];
    }
    foreach ($collect_detail as $xa) {
      // dd(count($xa));
      // dd($xa);
      foreach ($xa as $key => $d) {
        $cad = count($xa);
        // dd($d);
        // dd($ca);
        // $manifest_data= DB::table('manifest_details')
        // ->leftJoin('job_order_details','job_order_details.id','manifest_details.job_order_detail_id')
        // ->leftJoin('job_orders','job_orders.id','job_order_details.header_id')
        // ->leftJoin('manifests','manifests.id','manifest_details.header_id')
        // ->leftJoin('routes','routes.id','manifests.route_id')
        // ->leftJoin('cities','cities.id','routes.city_to')
        // ->where('job_order_details.item_name',$d->item_name)
        // ->where('job_order_details.qty',$d->item_qty)
        // ->where('job_orders.customer_id',$d->customer_id)
        // ->where('cities.id',$d->city_to_id)
        // ->whereBetween('manifests.date_manifest', [$startDate, $endDate])
        // ->get();
        // $ca_2=count($manifest_data);
        $countJ = DB::table('job_order_details')
          ->where('job_order_details.item_name', $d->item_name)
          ->where('job_order_details.qty', $d->item_qty)
          ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
          ->where('job_orders.customer_id', $d->customer_id)
          ->select('job_order_details.id as id')
          ->get();
        $id = $countJ->pluck('id')->toArray();
        $count = DB::table('manifest_details')->whereIn('job_order_detail_id', $id)->get();
        // dd(count($count));
        // dd(count($manifest_data));
        // dd($ca);
        // $ca=count($d);
        $countData = count($count);
        // dd($countData);
        $total += $d->total_price;
        $total_qty = $ca_qty;
      }
    }
    $data['total_data'] = $totalData;
    $data['total_qty_data'] = $ca_qty;
    $data['company'] = $company;
    $data['customer'] = $customer;
    $data['invoice'] = $invoice;
    $data['detail'] = $detail;
    $data['start'] = $startDate;
    $data['end'] = $endDate;
    $data['detail_item'] = $detail_item;
    // dd($data);
    // dd($detail);
    if ($name == 2) {
      return PDF::loadView('pdf.rekap_sj_per_nopol', $data)
        ->setPaper('A4', 'landscape')
        ->stream('Rekap Surat Jalan.pdf');
    } else if ($name == 1) {
      return PDF::loadView('pdf.rekap_sj_per_sopir', $data)
        ->setPaper('A4', 'landscape')
        ->stream('Rekap Surat Jalan.pdf');
    } else if ($name == 3) {
      return PDF::loadView('pdf.rekap_sj_per_pelanggan', $data)
        ->setPaper('A4')
        ->stream('Rekap Surat Jalan.pdf');
    }
  }
  /**
   * Date : 28-03-2022
   * Function : Export invoice to excel
   * Last Change : Export invoice to excel
   * Developer : Dimas Ihsan
   * Status : Create
   */
  public function export(Request $request)
  {
    $rtype = $request->format;
    switch ($rtype) {
      case 1: // Per Botol/KG/RIT
        return $this->format_1($request);
        break;
      case 2: // Per PLT
        return $this->format_2($request);
        break;
      case 3: // Per FB
        return $this->format_3($request);
        break;
      default:
        return null;
        break;
    }
  }
  public function format_1(Request $request)
  {
    return Excel::download(new InvoiceExport(AppInvoice::getDataInvoicePerBotol($request), 1), 'INVOICE PER BOTOL KG RIT ' . Carbon::now()->format("d-m-Y His") . '.xlsx');
  }
  public function format_2(Request $request)
  {
    return Excel::download(new InvoiceExport(AppInvoice::getDataInvoicePerPLT($request), 2), 'INVOICE PER PLT ' . Carbon::now()->format("d-m-Y His") . '.xlsx');
  }
  public function format_3(Request $request)
  {
    return Excel::download(new InvoiceExport(AppInvoice::getDataInvoicePerFB($request), 3), 'INVOICE PER FB ' . Carbon::now()->format("d-m-Y His") . '.xlsx');
  }
  // public function format_2($params)
  // {
  //   return Excel::download(new Invoice2Export($params), 'INVOICE ('.Carbon::now()->format("H:i d-m-Y").').xlsx');
  // }
  // public function format_3($params)
  // {
  //   return Excel::download(new Invoice2Export($params), 'INVOICE ('.Carbon::now()->format("H:i d-m-Y").').xlsx');
  // }
  public function get_commodity($customer_id)
  {
    $data = DB::table('job_order_details as jod')
      ->leftJoin('job_orders as jo', 'jo.id', 'jod.header_id')
      ->leftJoin('commodities', 'commodities.id', 'jod.commodity_id')
      ->where('customer_id', $customer_id)
      ->groupBy('jod.commodity_id')
      ->select('commodities.name', 'commodities.id')
      ->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function print_tagihan_customer(Request $request)
  {
    $company = DB::table('companies')->where('id', auth()->user()->company_id)->first();
    $customer = DB::table('contacts')->where('id', $request->customer_id)->first();
    $detail = InvoiceDetail::where('invoices.customer_id', $request->customer_id)
      ->leftJoin('invoices', 'invoices.id', 'invoice_details.header_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    if ($request->commodity_id) {
      $detail = $detail->where('job_order_details.commodity_id', $request->commodity_id);
    }
    if ($request->invoice_id) {
      $detail = $detail->where('invoices.id', $request->invoice_id);
    }
    if ($request->start_date != null) {
      $start = Carbon::parse($request->start_date)->format('Y-m-d');
      $data['start_date'] = $start;
      $detail = $detail->where(DB::raw('DATE_FORMAT(invoices.created_at, "%Y-%m-%d")'), '>=', $start);
    } else {
      $data['start_date'] = null;
    }
    if ($request->end_date != null) {
      $end = Carbon::parse($request->end_date)->format('Y-m-d');
      $data['end_date'] = $end;
      $detail = $detail->where(DB::raw('DATE_FORMAT(invoices.created_at, "%Y-%m-%d")'), '<=', $end);
    } else {
      $data['end_date'] = null;
    }
    // $detail= $detail->groupBy('job_orders.id');
    $detail = $detail->selectRaw('
        invoice_details.*,
        from.name as asal,
        to.name as tujuan,
        COUNT(job_orders.id) as jumlah_rit,
        job_order_details.item_name as item_name,
        SUM(invoice_details.qty * invoice_details.price) as total_price
      ')
      ->where('item_name', '!=', '')
      ->groupBy('manifests.route_id', 'commodity_name')
      ->get();
    $data['company'] = $company;
    $data['customer'] = $customer;
    $data['detail'] = $detail;
    return PDF::loadView('pdf.tagihan_customer', $data)
      ->setPaper('A4')
      ->stream('Tagihan per Customer.pdf');
    // return view('pdf.tagihan_customer',compact('data'));
  }
  public function cetak_invoice_ftl($id)
  {
    $item = Invoice::with('company', 'customer', 'journal', 'jobOrders')
      ->leftJoin('tax_invoices', 'tax_invoices.invoice_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('invoices.*, tax_invoices.code AS tax_invoice_code')
      ->first();
    $detail = InvoiceDetail::where('invoice_details.header_id', $id)
      // ->leftJoin('invoices','invoices.id','invoice_details.header_id')
      // ->leftJoin('work_orders', 'work_orders.id', 'invoice_details.work_order_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      // ->leftJoin('job_orders', 'job_orders.work_order_id', 'work_orders.id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers as dod', 'dod.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    $detail = $detail->selectRaw('
        invoice_details.*,
        convert(invoice_details.qty, char) as qty_str,
        from.name as asal,
        to.name as tujuan,
        to.id as tujuan_id,
        job_orders.shipment_date,
        job_order_details.item_name as item_name,
        job_orders.total_price as price_jo,
        (invoice_details.qty * invoice_details.price) as total_price,
        vehicle_types.name as jenis_kendaraan,
        vehicles.nopol,
        manifest_details.customer_sj,
        job_orders.service_type_id,
        job_orders.service_id,
        (select count(z.id) from job_order_details as z where z.header_id = job_orders.id) as total_manifest
      ')
      ->orderBy('invoice_details.id', 'desc')
      ->get();
    $detail = collect($detail)->unique('id');
    $check = count($detail->whereIn('service_id', [92, 162, 165, 86, 93, 118, 161, 164, 183]));
    $start_date = $check ? $detail->min(fn($event) => $event->shipment_date) : '-';
    $end_date = $check ? $detail->max(fn($event) => $event->shipment_date) : '-';
    $data_detail = [];
    $asal = '';
    $sub_total = 0;
    $groupedData = $detail->whereIn('service_id', [86, 93, 118, 161, 164, 183])->groupBy(['asal', 'tujuan', 'item_name']);
    foreach ($groupedData as $keyAsal => $value) {
      foreach ($value as $keyTujuan => $val) {
        foreach ($val as $items) {
          $sub_total += $items[0]->price_jo * $items->sum('total_manifest');
          $data_detail[] = (object) [
            'asal' => $keyAsal != $asal ? $keyAsal : '-',
            'tujuan' => $items[0]->tujuan,
            'jenis_barang' => $items[0]->item_name,
            'jumlah' => $items->sum('total_manifest'),
            'harga' => $items[0]->price_jo,
          ];
          $asal = $keyAsal;
        }
      }
    }
    $data['item'] = $item;
    $data['start_date'] = $start_date;
    $data['end_date'] = $end_date;
    $data['detail'] = $data_detail;
    $data['sub_total'] = $sub_total;
    return PDF::loadView('pdf.cetak_invoice_ftl', $data)
      ->setPaper('A4')
      ->stream('Invoice_FTL.pdf');
  }
  public function cetak_invoice_non_palet($id)
  {
    $item = Invoice::with('company', 'customer', 'journal', 'jobOrders')
      ->leftJoin('tax_invoices', 'tax_invoices.invoice_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('invoices.*, tax_invoices.code AS tax_invoice_code')
      ->first();
    $detail = InvoiceDetail::where('invoice_details.header_id', $id)
      // ->leftJoin('invoices','invoices.id','invoice_details.header_id')
      // ->leftJoin('work_orders', 'work_orders.id', 'invoice_details.work_order_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      // ->leftJoin('job_orders', 'job_orders.work_order_id', 'work_orders.id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers as dod', 'dod.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    $detail = $detail->selectRaw('
        invoice_details.*,
        convert(invoice_details.qty, char) as qty_str,
        from.name as asal,
        to.name as tujuan,
        to.id as tujuan_id,
        job_orders.shipment_date,
        job_order_details.item_name as item_name,
        job_orders.total_price as price_jo,
        (invoice_details.qty * invoice_details.price) as total_price,
        vehicle_types.name as jenis_kendaraan,
        vehicles.nopol,
        manifest_details.customer_sj,
        job_orders.service_type_id,
        job_orders.service_id,
        (select count(z.id) from job_order_details as z where z.header_id = job_orders.id) as total_manifest
      ')
      ->groupBy('invoice_details.id')
      ->get();

    $detail = collect($detail)->unique('id');
    $data['detail'] = $detail;
    $check = count($detail->whereIn('service_id', [92, 162, 165, 86, 93, 118, 161, 164, 183]));
    $start_date = $check ? $detail->min(fn($event) => $event->shipment_date) : '-';
    $end_date = $check ? $detail->max(fn($event) => $event->shipment_date) : '-';
    $data_detail1 = [];
    $data_detail2 = [];
    $asal = '';
    $sub_total = 0;
    foreach ($detail->whereIn('service_id', [92, 162, 165])->groupBy(['asal', 'tujuan']) as $keyAsal => $value) {
      foreach ($value as $keyTujuan => $val) {
        $sub_total += $val->where('tujuan', $keyTujuan)[0]->price * $val->where('tujuan', $keyTujuan)->sum('qty');
        $data_detail1[] = (object) [
          'asal' => $keyAsal != $asal ? $keyAsal : '-',
          'tujuan' => $val->where('tujuan', $keyTujuan)[0]->tujuan,
          'jenis_barang' => $val->where('tujuan', $keyTujuan)[0]->item_name,
          'jumlah' => $val->where('tujuan', $keyTujuan)->sum('qty'),
          'harga' => $val->where('tujuan', $keyTujuan)[0]->price,
        ];
        $asal = $keyAsal;
      }
    }
    $asal = '';
    foreach ($detail->whereIn('service_id', [86, 93, 118, 161, 164, 183])->groupBy(['asal', 'tujuan', 'item_name']) as $keyAsal => $value) {
      foreach ($value as $keyTujuan => $val) {
        foreach ($val as $items) {
          $sub_total += $items[0]->price_jo * $items->sum('total_manifest');
          $data_detail2[] = (object) [
            'asal' => $keyAsal != $asal ? $keyAsal : '-',
            'tujuan' => $items[0]->tujuan,
            'jenis_barang' => $items[0]->item_name,
            'jumlah' => $items->sum('total_manifest'),
            'harga' => $items[0]->price_jo,
          ];
          $asal = $keyAsal;
        }
      }
    }
    $data['item'] = $item;
    $data['start_date'] = $start_date;
    $data['end_date'] = $end_date;
    $data['detail1'] = $data_detail1;
    $data['detail2'] = $data_detail2;
    $data['sub_total'] = $sub_total;
    return PDF::loadView('pdf.cetak_invoice_non_palet', $data)
      ->setPaper('A4')
      ->stream('Invoice_Non_Palet_Based.pdf');
  }
  public function cetak_invoice_palet_based($id)
  {
    $item = Invoice::with('company', 'customer', 'journal', 'jobOrders')->find($id);
    $tax_invoice = DB::table('tax_invoices')->where('invoice_id', $id)->first();
    if ($tax_invoice) {
      $item->tax_invoice_code = $tax_invoice->code;
    } else {
      $item->tax_invoice_code = null;
    }
    $detail = InvoiceDetail::where('invoice_details.header_id', $id)
      // ->leftJoin('invoices','invoices.id','invoice_details.header_id')
      // ->leftJoin('work_orders', 'work_orders.id', 'invoice_details.work_order_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      // ->leftJoin('job_orders', 'job_orders.work_order_id', 'work_orders.id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers as dod', 'dod.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    $detail = $detail->selectRaw('
        invoice_details.*,
        convert(invoice_details.qty, char) as qty_str,
        from.name as asal,
        to.name as tujuan,
        to.id as tujuan_id,
        job_orders.shipment_date,
        job_order_details.item_name as item_name,
        job_orders.total_price as price_jo,
        (invoice_details.qty * invoice_details.price) as total_price,
        vehicle_types.name as jenis_kendaraan,
        vehicles.nopol,
        manifest_details.customer_sj,
        job_orders.service_type_id,
        job_orders.service_id,
        (select count(z.id) from job_order_details as z where z.header_id = job_orders.id) as total_manifest
      ')
      ->get();
    $detail = collect($detail)->unique('id');

    $check = count($detail->whereIn('service_id', [92, 162, 165, 86, 93, 118, 161, 164, 183]));

    $start_date = $check ? $detail->min(fn($event) => $event->shipment_date) : '-';
    $end_date = $check ? $detail->max(fn($event) => $event->shipment_date) : '-';
    $data_detail1 = [];
    $data_detail2 = [];
    $asal = '';
    $sub_total = 0;
    $no = 1;
    $detail1_data = $detail->whereIn('service_id', [92, 162, 165])
                          ->whereIn('qty', [1, 2, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18]);

    foreach ($detail1_data->groupBy('tujuan') as $destination => $detailWithDestination) {

      $pallet_counts = [];
      $total_price = 0;

      foreach ($detailWithDestination as $detail_item) {
        $qty = $detail_item->qty;
        if (!isset($pallet_counts[$qty])) {
          $pallet_counts[$qty] = 0;
        }
        $pallet_counts[$qty]++;
        $total_price += $detail_item->total_price;
      }

      $sub_total += $total_price;

      $data_detail1[] = (object) [
        'no' => $no,
        'tujuan' => $destination,
        'pallet1' => isset($pallet_counts[1]) ? $pallet_counts[1] : '-',
        'pallet2' => isset($pallet_counts[2]) ? $pallet_counts[2] : '-',
        'pallet7' => isset($pallet_counts[7]) ? $pallet_counts[7] : '-',
        'pallet10' => isset($pallet_counts[10]) ? $pallet_counts[10] : '-',
        'pallet11' => isset($pallet_counts[11]) ? $pallet_counts[11] : '-',
        'pallet12' => isset($pallet_counts[12]) ? $pallet_counts[12] : '-',
        'pallet13' => isset($pallet_counts[13]) ? $pallet_counts[13] : '-',
        'pallet14' => isset($pallet_counts[14]) ? $pallet_counts[14] : '-',
        'pallet15' => isset($pallet_counts[15]) ? $pallet_counts[15] : '-',
        'pallet16' => isset($pallet_counts[16]) ? $pallet_counts[16] : '-',
        'pallet17' => isset($pallet_counts[17]) ? $pallet_counts[17] : '-',
        'pallet18' => isset($pallet_counts[18]) ? $pallet_counts[18] : '-',
        'harga' => $detailWithDestination->first()->total_price,
        'total_harga' => $total_price
      ];

      $no++;
    }
    // Menampilkan setiap rit sebagai baris terpisah, bukan dikelompokkan
    $detail2_items = $detail->whereIn('service_id', [86, 93, 118, 161, 164, 183]);

    foreach ($detail2_items as $detail_item) {
      // Setiap job order akan ditampilkan sebanyak total_manifest (jumlah rit)
      for ($i = 1; $i <= $detail_item->total_manifest; $i++) {
        $sub_total += $detail_item->price_jo;
        $data_detail2[] = (object) [
          'no' => $no++,
          'asal' => $detail_item->asal != $asal ? $detail_item->asal : '-',
          'tujuan' => $detail_item->tujuan,
          'jenis_barang' => $detail_item->item_name,
          'jumlah' => 1, // Setiap baris menampilkan 1 rit
          'harga' => $detail_item->price_jo,
        ];
        $asal = $detail_item->asal;
      }
    }
    $data['item'] = $item;
    $data['start_date'] = $start_date;
    $data['end_date'] = $end_date;
    $data['detail1'] = $data_detail1;
    $data['detail2'] = $data_detail2;
    $data['sub_total'] = $sub_total;

    return PDF::loadView('pdf.cetak_invoice_palet_based', $data)
      ->setPaper('A4')
      ->stream('Invoice_Palet_Based.pdf');
  }
  public function cetak_pre_inv($id)
  {
    $item = Invoice::with('company', 'customer', 'journal', 'jobOrders')
      ->leftJoin('tax_invoices', 'tax_invoices.invoice_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('invoices.*, tax_invoices.code AS tax_invoice_code')
      ->first();
    // Subquery for PPN taxes
    $ppn = DB::table('invoice_taxes')
      ->join('taxes', 'taxes.id', 'invoice_taxes.tax_id')
      ->where('taxes.is_ppn', 1)
      ->selectRaw('
                    invoice_taxes.invoice_detail_id,
                    SUM(invoice_taxes.amount) as amount
                ')
      ->groupBy('invoice_taxes.invoice_detail_id');

    // Subquery for PPh taxes
    $pph = DB::table('invoice_taxes')
      ->join('taxes', 'taxes.id', 'invoice_taxes.tax_id')
      ->where('taxes.is_ppn', 0)
      ->selectRaw('
                    invoice_taxes.invoice_detail_id,
                    SUM(invoice_taxes.amount) as amount
                ')
      ->groupBy('invoice_taxes.invoice_detail_id');
    $detail = InvoiceDetail::where('invoices.id', $id)
      ->leftJoin('invoices', 'invoices.id', 'invoice_details.header_id')
      ->leftJoinSub($ppn, 'ppn', 'ppn.invoice_detail_id', 'invoice_details.id')
      ->leftJoinSub($pph, 'pph', 'pph.invoice_detail_id', 'invoice_details.id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers as dod', 'dod.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    $detail = $detail->selectRaw('
        invoice_details.*,
        convert(invoice_details.qty, char) as qty_str,
        from.name as asal,
        to.name as tujuan,
        to.id as tujuan_id,
        job_orders.shipment_date,
        job_order_details.item_name as item_name,
        (invoice_details.qty * invoice_details.price) as total_price,
        vehicle_types.name as jenis_kendaraan,
        vehicles.nopol,
        manifest_details.customer_sj,
        COALESCE(ppn.amount, 0) as ppn_tax,
        COALESCE(pph.amount, 0) as pph_tax
      ')
      ->get();
    $data['item'] = $item;
    $data['detail'] = $detail->unique('id');
    return PDF::loadView('pdf.cetak_pre_inv', $data)
      ->setPaper('A4')
      ->stream('Pre-Inv.pdf');
  }
  public function print_fastana(Request $request, $id)
  {
    $item = Invoice::with('company', 'customer', 'journal', 'jobOrders')
      ->leftJoin('tax_invoices', 'tax_invoices.invoice_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('invoices.*, tax_invoices.code AS tax_invoice_code')
      ->first();
    $detail = InvoiceDetail::where('invoices.id', $id)
      ->leftJoin('invoices', 'invoices.id', 'invoice_details.header_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers as dod', 'dod.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    $detail = $detail->selectRaw('
        invoice_details.*,
        from.name as asal,
        to.name as tujuan,
        job_orders.shipment_date,
        job_order_details.item_name as item_name,
        (invoice_details.qty * invoice_details.price) as total_price,
        vehicle_types.name as jenis_kendaraan,
        vehicles.nopol,
        manifest_details.customer_sj
      ')
      ->get();
    $data['item'] = $item;
    $data['detail'] = $detail;
    $data['show_tonase'] = $request->show_tonase;
    return PDF::loadView('pdf.invoice_fastana', $data)
      ->setPaper('A4')
      ->stream('Tagihan per Customer.pdf');
    // return view('pdf.tagihan_customer',compact('data'));
  }
  public function export_fastana(Request $request, $id)
  {
    $item = Invoice::with('company', 'customer', 'journal', 'jobOrders')
      ->leftJoin('tax_invoices', 'tax_invoices.invoice_id', 'invoices.id')
      ->where('invoices.id', $id)
      ->selectRaw('invoices.*, tax_invoices.code AS tax_invoice_code')
      ->first();
    $detail = InvoiceDetail::where('invoices.id', $id)
      ->leftJoin('invoices', 'invoices.id', 'invoice_details.header_id')
      ->leftJoin('job_orders', 'job_orders.id', 'invoice_details.job_order_id')
      ->leftJoin('job_order_details', 'job_order_details.header_id', 'job_orders.id')
      ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', 'job_order_details.id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
      ->leftJoin('delivery_order_drivers as dod', 'dod.manifest_id', 'manifests.id')
      ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id')
      ->leftJoin('routes as r', 'r.id', 'manifests.route_id')
      ->leftJoin('cities as from', 'from.id', 'r.city_from')
      ->leftJoin('cities as to', 'to.id', 'r.city_to');
    $detail = $detail->selectRaw('
        invoice_details.*,
        from.name as asal,
        to.name as tujuan,
        job_orders.shipment_date,
        job_order_details.item_name as item_name,
        (invoice_details.qty * invoice_details.price) as total_price,
        vehicle_types.name as jenis_kendaraan,
        vehicles.nopol,
        manifest_details.customer_sj
      ')
      ->get();
    $data['item'] = $item;
    $data['detail'] = $detail;
    $data['show_tonase'] = $request->show_tonase;
    return Excel::download(new InvoiceFastanaExport($data), 'INVOICE FASTANA ' . Carbon::now()->format("d-m-Y His") . '.xlsx');
  }
}
